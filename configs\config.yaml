# 主配置文件
# 数据增强故障诊断项目配置

# 实验设置
experiment:
  name: "fault_diagnosis_data_augmentation"
  description: "使用CDDPM和其他生成模型进行一维振动信号数据增强的故障诊断研究"
  seed: 42
  device: "cuda"  # cuda, cpu, auto
  mixed_precision: true
  
# 数据集配置
dataset:
  # 当前使用的数据集
  name: "KAT"  # KAT, SK, JST
  
  # 数据集信息
  datasets:
    KAT:
      num_classes: 8
      signal_length: 1024
      train_samples: 1600
      test_samples: 1600
      data_type: "float64"
      label_type: "int32"
    SK:
      num_classes: 3
      signal_length: 1024
      train_samples: 336
      test_samples: 144
      data_type: "float64"
      label_type: "int32"
    JST:
      num_classes: 3
      signal_length: 1024
      train_samples: 600
      test_samples: 600
      data_type: "uint16"
      label_type: "int32"
  
  # 数据加载参数
  data_loading:
    # 样本截取长度（从原始信号长度中截取）
    truncate_length: 512  # 如果为null则使用完整长度
    # 加载的样本数量限制
    max_train_samples: null  # 如果为null则使用全部样本
    max_test_samples: null   # 如果为null则使用全部样本
    # 数据预处理
    normalize: true
    standardize: false
    # 噪声添加（用于噪声鲁棒性分析）
    add_noise: false
    noise_level: 0.1  # 噪声强度
    noise_type: "gaussian"  # gaussian, uniform
  
  # 数据增强配置
  augmentation:
    # 是否启用数据增强
    enabled: true
    # 生成样本数量（每个类别）
    generated_samples_per_class: 200
    # 使用的增强方法
    methods: ["CDDPM", "CGAN", "WGAN", "WGAN-GP", "ACGAN", "DDPM", "ADASYN", "SMOTEENN"]
    # 当前使用的方法
    current_method: "CDDPM"

# 模型配置
models:
  # 分类器配置 (MR-CNN)
  classifier:
    name: "MRCNN"
    kernel_sizes: [3, 5, 7]
    base_channels: 64
    dropout: 0.1
    
  # 生成模型配置
  generators:
    # CDDPM配置
    CDDPM:
      model_type: "unet1d"
      hidden_dim: 128
      num_layers: 4
      num_heads: 8
      time_steps: 1000
      beta_schedule: "cosine"
      loss_type: "mse"
      
    # CGAN配置
    CGAN:
      generator:
        noise_dim: 100
        hidden_dims: [256, 512, 1024]
        output_activation: "tanh"
      discriminator:
        hidden_dims: [1024, 512, 256]
        dropout: 0.3
        
    # WGAN配置
    WGAN:
      generator:
        noise_dim: 100
        hidden_dims: [256, 512, 1024]
        output_activation: "tanh"
      discriminator:
        hidden_dims: [1024, 512, 256]
        dropout: 0.3
      clip_value: 0.01
      
    # WGAN-GP配置
    WGAN_GP:
      generator:
        noise_dim: 100
        hidden_dims: [256, 512, 1024]
        output_activation: "tanh"
      discriminator:
        hidden_dims: [1024, 512, 256]
        dropout: 0.3
      lambda_gp: 10
      
    # ACGAN配置
    ACGAN:
      generator:
        noise_dim: 100
        hidden_dims: [256, 512, 1024]
        output_activation: "tanh"
      discriminator:
        hidden_dims: [1024, 512, 256]
        dropout: 0.3
      lambda_cls: 1.0
      
    # DDPM配置
    DDPM:
      model_type: "unet1d"
      hidden_dim: 128
      num_layers: 4
      time_steps: 1000
      beta_schedule: "linear"
      
    # 传统方法配置
    ADASYN:
      n_neighbors: 5
      random_state: 42
      
    SMOTEENN:
      smote_k_neighbors: 5
      enn_n_neighbors: 3
      random_state: 42

# 训练配置
training:
  # 通用训练参数
  batch_size: 32
  num_epochs: 100
  learning_rate: 0.001
  weight_decay: 1e-4
  
  # 优化器配置
  optimizer:
    type: "Adam"  # Adam, SGD, AdamW
    betas: [0.9, 0.999]
    eps: 1e-8
    
  # 学习率调度
  scheduler:
    type: "CosineAnnealingLR"  # StepLR, CosineAnnealingLR, ReduceLROnPlateau
    T_max: 100
    eta_min: 1e-6
    
  # 早停配置
  early_stopping:
    enabled: true
    patience: 15
    min_delta: 1e-4
    monitor: "val_accuracy"
    mode: "max"
    
  # 检查点保存
  checkpoint:
    save_best: true
    save_process: true
    process_intervals: 10  # 总训练步骤分10个过程保存
    save_frequency: 5  # 每5个epoch保存一次过程权重
    
  # 生成模型特定训练参数
  generator_training:
    # CDDPM训练参数
    CDDPM:
      num_epochs: 200
      learning_rate: 1e-4
      batch_size: 64
      
    # GAN系列训练参数
    GAN:
      num_epochs: 200
      g_learning_rate: 2e-4
      d_learning_rate: 2e-4
      batch_size: 64
      n_critic: 5  # 判别器训练次数

# 评估配置
evaluation:
  # 评估指标
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "confusion_matrix"
    - "gan_train"
    - "gan_test"
    
  # 可视化配置
  visualization:
    tsne:
      enabled: true
      perplexity: 30
      n_iter: 1000
      random_state: 42
    confusion_matrix:
      enabled: true
      normalize: true
    
  # 计算性能指标
  performance_metrics:
    measure_time: true
    measure_gpu_usage: true
    measure_flops: true
    
  # 交叉验证（暂时禁用）
  cross_validation:
    enabled: false
    n_folds: 5
    random_state: 42

# 实验分析配置
analysis:
  # 原始样本数量分析
  sample_count_analysis:
    enabled: true
    sample_ratios: [0.1, 0.2, 0.5, 0.8, 1.0]  # 使用原始数据的比例
    
  # 生成样本数量分析
  generated_count_analysis:
    enabled: true
    generated_counts: [50, 100, 200, 500, 1000]  # 每类生成的样本数
    
  # 噪声鲁棒性分析
  noise_analysis:
    enabled: true
    noise_levels: [0.0, 0.05, 0.1, 0.15, 0.2, 0.25]
    
  # 消融实验
  ablation_study:
    enabled: true
    # CDDPM消融实验
    CDDPM_ablation:
      time_steps: [100, 500, 1000, 2000]
      beta_schedules: ["linear", "cosine", "sigmoid"]
      model_sizes: ["small", "medium", "large"]

# 批量实验配置
batch_experiments:
  # 组合实验
  combinations:
    # 实验1：样本数量 + 生成方法
    exp1:
      name: "sample_count_vs_methods"
      sample_ratios: [0.2, 0.5, 1.0]
      methods: ["CDDPM", "CGAN", "WGAN-GP"]
      
    # 实验2：生成数量 + 噪声水平
    exp2:
      name: "generated_count_vs_noise"
      generated_counts: [100, 200, 500]
      noise_levels: [0.0, 0.1, 0.2]
      
    # 实验3：方法对比
    exp3:
      name: "method_comparison"
      methods: ["CDDPM", "CGAN", "WGAN", "WGAN-GP", "ACGAN", "DDPM", "ADASYN", "SMOTEENN"]

# 输出配置
output:
  # 结果保存
  save_results: true
  save_models: true
  save_generated_samples: true
  
  # 日志配置
  logging:
    level: "INFO"  # DEBUG, INFO, WARNING, ERROR
    save_to_file: true
    console_output: true
    
  # 可视化保存
  save_plots: true
  plot_format: "png"  # png, pdf, svg
  plot_dpi: 300
