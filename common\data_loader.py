"""
数据加载器模块
支持多数据集加载、数据预处理、样本截取等功能
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import logging
from typing import Tuple, Optional, List, Dict, Any
import random

logger = logging.getLogger(__name__)


class FaultDiagnosisDataset(Dataset):
    """故障诊断数据集类"""
    
    def __init__(
        self,
        data: np.ndarray,
        labels: np.ndarray,
        transform=None,
        target_transform=None
    ):
        """
        初始化数据集
        
        Args:
            data: 输入数据，形状为 (n_samples, signal_length)
            labels: 标签数据，形状为 (n_samples,)
            transform: 数据变换函数
            target_transform: 标签变换函数
        """
        self.data = torch.FloatTensor(data)
        self.labels = torch.LongTensor(labels.flatten())
        self.transform = transform
        self.target_transform = target_transform
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        label = self.labels[idx]
        
        if self.transform:
            sample = self.transform(sample)
        if self.target_transform:
            label = self.target_transform(label)
            
        return sample, label


class DataLoaderManager:
    """数据加载管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据加载管理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.dataset_config = config['dataset']
        self.data_loading_config = config['dataset']['data_loading']
        
        # 数据集根目录
        self.dataset_root = "dataset"
        
        # 支持的数据集
        self.supported_datasets = ['KAT', 'SK', 'JST']
        
        # 数据预处理器
        self.scaler = None
        
    def load_dataset(
        self,
        dataset_name: str,
        data_type: str = "train",
        apply_truncation: bool = True,
        apply_sample_limit: bool = True,
        add_noise: bool = False
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载指定数据集
        
        Args:
            dataset_name: 数据集名称 (KAT, SK, JST)
            data_type: 数据类型 (train, test)
            apply_truncation: 是否应用信号截取
            apply_sample_limit: 是否应用样本数量限制
            add_noise: 是否添加噪声
            
        Returns:
            data: 数据数组，形状为 (n_samples, signal_length)
            labels: 标签数组，形状为 (n_samples,)
        """
        if dataset_name not in self.supported_datasets:
            raise ValueError(f"不支持的数据集: {dataset_name}. 支持的数据集: {self.supported_datasets}")
        
        # 构建文件路径
        data_path = os.path.join(
            self.dataset_root,
            dataset_name,
            "used_npy",
            data_type,
            f"{dataset_name.lower()}_data.npy"
        )
        label_path = os.path.join(
            self.dataset_root,
            dataset_name,
            "used_npy", 
            data_type,
            f"{dataset_name.lower()}_label.npy"
        )
        
        # 检查文件是否存在
        if not os.path.exists(data_path) or not os.path.exists(label_path):
            raise FileNotFoundError(f"数据文件不存在: {data_path} 或 {label_path}")
        
        # 加载数据
        logger.info(f"加载数据集: {dataset_name} ({data_type})")
        data = np.load(data_path)
        labels = np.load(label_path)
        
        # 数据类型转换
        if data.dtype == np.uint16:  # JST数据集特殊处理
            data = data.astype(np.float32)
        else:
            data = data.astype(np.float32)
        
        labels = labels.astype(np.int64)
        
        logger.info(f"原始数据形状: {data.shape}, 标签形状: {labels.shape}")
        logger.info(f"数据类型: {data.dtype}, 标签类型: {labels.dtype}")
        
        # 获取标签分布
        unique_labels, counts = np.unique(labels, return_counts=True)
        logger.info(f"标签分布: {dict(zip(unique_labels, counts))}")
        
        # 应用样本数量限制
        if apply_sample_limit:
            data, labels = self._apply_sample_limit(data, labels, data_type)
        
        # 应用信号截取
        if apply_truncation:
            data = self._apply_truncation(data)
        
        # 添加噪声
        if add_noise:
            data = self._add_noise(data)
        
        # 数据预处理
        data = self._preprocess_data(data, data_type)
        
        logger.info(f"最终数据形状: {data.shape}, 标签形状: {labels.shape}")
        
        return data, labels
    
    def _apply_sample_limit(
        self,
        data: np.ndarray,
        labels: np.ndarray,
        data_type: str
    ) -> Tuple[np.ndarray, np.ndarray]:
        """应用样本数量限制"""
        if data_type == "train":
            max_samples = self.data_loading_config.get('max_train_samples')
        else:
            max_samples = self.data_loading_config.get('max_test_samples')
        
        if max_samples is not None and max_samples < len(data):
            if max_samples > len(data):
                logger.warning(f"设置的样本数量 {max_samples} 超过了最大数量 {len(data)}，使用最大数量")
                max_samples = len(data)
            
            # 随机选择样本，保持类别平衡
            indices = self._balanced_sample_indices(labels, max_samples)
            data = data[indices]
            labels = labels[indices]
            
            logger.info(f"应用样本限制: {len(data)} 个样本")
        
        return data, labels
    
    def _balanced_sample_indices(self, labels: np.ndarray, max_samples: int) -> np.ndarray:
        """平衡采样索引"""
        unique_labels = np.unique(labels)
        samples_per_class = max_samples // len(unique_labels)
        remaining_samples = max_samples % len(unique_labels)
        
        indices = []
        for i, label in enumerate(unique_labels):
            label_indices = np.where(labels == label)[0]
            n_samples = samples_per_class + (1 if i < remaining_samples else 0)
            n_samples = min(n_samples, len(label_indices))
            
            selected_indices = np.random.choice(label_indices, n_samples, replace=False)
            indices.extend(selected_indices)
        
        return np.array(indices)
    
    def _apply_truncation(self, data: np.ndarray) -> np.ndarray:
        """应用信号截取"""
        truncate_length = self.data_loading_config.get('truncate_length')
        
        if truncate_length is not None and truncate_length < data.shape[1]:
            logger.info(f"截取信号长度: {data.shape[1]} -> {truncate_length}")
            
            # 随机截取
            truncated_data = []
            for sample in data:
                start_idx = random.randint(0, len(sample) - truncate_length)
                truncated_sample = sample[start_idx:start_idx + truncate_length]
                truncated_data.append(truncated_sample)
            
            data = np.array(truncated_data)
        
        return data
    
    def _add_noise(self, data: np.ndarray) -> np.ndarray:
        """添加噪声"""
        if not self.data_loading_config.get('add_noise', False):
            return data
        
        noise_level = self.data_loading_config.get('noise_level', 0.1)
        noise_type = self.data_loading_config.get('noise_type', 'gaussian')
        
        logger.info(f"添加噪声: 类型={noise_type}, 强度={noise_level}")
        
        if noise_type == 'gaussian':
            noise = np.random.normal(0, noise_level, data.shape)
        elif noise_type == 'uniform':
            noise = np.random.uniform(-noise_level, noise_level, data.shape)
        else:
            raise ValueError(f"不支持的噪声类型: {noise_type}")
        
        # 添加噪声
        noisy_data = data + noise
        
        return noisy_data.astype(np.float32)
    
    def _preprocess_data(self, data: np.ndarray, data_type: str) -> np.ndarray:
        """数据预处理"""
        # 归一化
        if self.data_loading_config.get('normalize', True):
            # 对每个样本进行归一化到 [-1, 1]
            data_min = data.min(axis=1, keepdims=True)
            data_max = data.max(axis=1, keepdims=True)
            data = 2 * (data - data_min) / (data_max - data_min + 1e-8) - 1
        
        # 标准化
        if self.data_loading_config.get('standardize', False):
            if data_type == "train":
                # 训练时拟合标准化器
                self.scaler = StandardScaler()
                data = self.scaler.fit_transform(data)
            else:
                # 测试时使用训练时的标准化器
                if self.scaler is not None:
                    data = self.scaler.transform(data)
        
        return data
    
    def create_dataloader(
        self,
        dataset_name: str,
        data_type: str = "train",
        batch_size: Optional[int] = None,
        shuffle: bool = True,
        num_workers: int = 0,
        **kwargs
    ) -> DataLoader:
        """
        创建数据加载器
        
        Args:
            dataset_name: 数据集名称
            data_type: 数据类型 (train, test)
            batch_size: 批次大小
            shuffle: 是否打乱数据
            num_workers: 工作进程数
            **kwargs: 其他参数
            
        Returns:
            DataLoader对象
        """
        # 加载数据
        data, labels = self.load_dataset(dataset_name, data_type, **kwargs)
        
        # 创建数据集
        dataset = FaultDiagnosisDataset(data, labels)
        
        # 获取批次大小
        if batch_size is None:
            batch_size = self.config['training']['batch_size']
        
        # 创建数据加载器
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )
        
        return dataloader
    
    def get_dataset_info(self, dataset_name: str) -> Dict[str, Any]:
        """获取数据集信息"""
        if dataset_name not in self.dataset_config['datasets']:
            raise ValueError(f"未配置的数据集: {dataset_name}")
        
        return self.dataset_config['datasets'][dataset_name]
    
    def load_generated_samples(
        self,
        dataset_name: str,
        method_name: str,
        sample_count: Optional[int] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        加载生成的样本
        
        Args:
            dataset_name: 数据集名称
            method_name: 生成方法名称
            sample_count: 样本数量限制
            
        Returns:
            生成的数据和标签
        """
        gen_samples_dir = os.path.join(
            self.dataset_root,
            dataset_name,
            "gen_samples",
            method_name
        )
        
        data_path = os.path.join(gen_samples_dir, "generated_data.npy")
        label_path = os.path.join(gen_samples_dir, "generated_labels.npy")
        
        if not os.path.exists(data_path) or not os.path.exists(label_path):
            raise FileNotFoundError(f"生成样本文件不存在: {data_path} 或 {label_path}")
        
        data = np.load(data_path)
        labels = np.load(label_path)
        
        # 应用样本数量限制
        if sample_count is not None and sample_count < len(data):
            indices = self._balanced_sample_indices(labels, sample_count)
            data = data[indices]
            labels = labels[indices]
        
        logger.info(f"加载生成样本: {method_name}, 数量: {len(data)}")
        
        return data, labels
