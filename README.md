# 一维振动信号故障诊断数据增强项目

基于条件去噪扩散概率模型(CDDPM)和多种生成对抗网络的一维振动信号数据增强故障诊断系统。

## 项目概述

本项目实现了论文《Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising Diffusion Probabilistic Model and Improved CNN》中提出的方法，并扩展支持多种数据增强技术，用于一维振动信号的故障诊断。

### 主要特性

- **多种数据增强方法**：CDDPM、CGAN、WGAN、WGAN-GP、ACGAN、DDPM、ADASYN、SMOTEENN
- **灵活的数据加载**：支持信号截取、样本数量限制、噪声添加
- **完整的评估体系**：准确率、精确率、召回率、F1分数、混淆矩阵、t-SNE、GAN指标
- **批量实验支持**：样本数量分析、生成数量分析、噪声鲁棒性分析、方法对比
- **可视化功能**：训练曲线、混淆矩阵、t-SNE降维、方法对比图
- **性能监控**：训练时间、GPU占用、内存使用

## 项目结构

```
├── configs/                 # 配置文件
│   └── config.yaml         # 主配置文件
├── common/                  # 通用模块
│   ├── data_loader.py      # 数据加载器
│   ├── trainer.py          # 分类器训练器
│   ├── gan_trainer.py      # GAN训练器
│   └── evaluation.py       # 评估模块
├── models/                  # 模型定义
│   ├── mr_cnn.py           # MR-CNN分类器
│   ├── cddpm.py            # CDDPM模型
│   ├── gan_models.py       # GAN系列模型
│   └── traditional_augmentation.py  # 传统增强方法
├── dataset/                 # 数据集目录
│   ├── KAT/                # KAT数据集
│   ├── SK/                 # SK数据集
│   ├── JST/                # JST数据集
│   └── README.md           # 数据集说明
├── logs/                   # 训练日志
├── results/                # 实验结果
├── checkpoints/            # 模型权重
│   ├── best/              # 最佳模型
│   └── process/           # 过程保存
├── train.py               # 单独训练脚本
├── batch_train.py         # 批量训练脚本
├── test.py                # 测试脚本
└── README.md              # 项目说明
```

## 环境要求

### Python依赖

```bash
pip install torch torchvision torchaudio
pip install numpy pandas matplotlib seaborn
pip install scikit-learn imbalanced-learn
pip install pyyaml tqdm psutil gputil
pip install tensorboard  # 可选，用于训练监控
```

### 硬件要求

- **开发/调试**：NVIDIA GTX 4060 8GB或同等性能GPU
- **最终测试**：NVIDIA RTX 4090或同等性能GPU
- **内存**：建议16GB以上系统内存

## 快速开始

### 1. 数据准备

确保数据集按照以下结构组织：

```
dataset/
├── KAT/
│   └── used_npy/
│       ├── train/
│       │   ├── kat_data.npy
│       │   └── kat_label.npy
│       └── test/
│           ├── kat_data.npy
│           └── kat_label.npy
├── SK/
└── JST/
```

### 2. 配置设置

编辑 `configs/config.yaml` 文件：

```yaml
# 选择数据集
dataset:
  name: "KAT"  # KAT, SK, JST

# 数据加载参数
dataset:
  data_loading:
    truncate_length: 512      # 信号截取长度
    max_train_samples: null   # 训练样本数量限制
    max_test_samples: null    # 测试样本数量限制

# 数据增强配置
dataset:
  augmentation:
    enabled: true
    current_method: "CDDPM"   # 当前使用的方法
    generated_samples_per_class: 200  # 每类生成样本数
```

### 3. 训练模型

#### 单独训练

```bash
# 训练生成器
python train.py --mode generator --method CDDPM

# 训练分类器（使用数据增强）
python train.py --mode classifier

# 训练分类器（不使用数据增强）
python train.py --mode classifier --no-augmentation

# 完整训练流程
python train.py --mode all
```

#### 批量训练

```bash
# 运行所有分析实验
python batch_train.py --experiments all

# 运行特定实验
python batch_train.py --experiments sample_count method_comparison

# 运行组合实验
python batch_train.py --experiments combination
```

### 4. 测试模型

```bash
# 单次测试
python test.py --checkpoint checkpoints/best/best_model.pth --mode single

# 综合测试
python test.py --checkpoint checkpoints/best/best_model.pth --mode comprehensive \
               --test-datasets KAT SK JST \
               --test-methods CDDPM CGAN WGAN-GP
```

## 配置说明

### 数据集配置

```yaml
dataset:
  name: "KAT"  # 当前使用的数据集
  
  datasets:
    KAT:
      num_classes: 8
      signal_length: 1024
      train_samples: 1600
      test_samples: 1600
    SK:
      num_classes: 3
      signal_length: 1024
      train_samples: 336
      test_samples: 144
    JST:
      num_classes: 3
      signal_length: 1024
      train_samples: 600
      test_samples: 600
```

### 模型配置

```yaml
models:
  # 分类器配置
  classifier:
    name: "MRCNN"
    kernel_sizes: [3, 5, 7]
    base_channels: 64
    dropout: 0.1
  
  # 生成器配置
  generators:
    CDDPM:
      hidden_dim: 128
      num_layers: 4
      time_steps: 1000
      beta_schedule: "cosine"
```

### 训练配置

```yaml
training:
  batch_size: 32
  num_epochs: 100
  learning_rate: 0.001
  
  # 早停配置
  early_stopping:
    enabled: true
    patience: 15
    monitor: "val_accuracy"
```

## 实验分析

### 1. 样本数量分析

分析不同原始样本数量对模型性能的影响：

```yaml
analysis:
  sample_count_analysis:
    enabled: true
    sample_ratios: [0.1, 0.2, 0.5, 0.8, 1.0]
```

### 2. 生成样本数量分析

分析不同生成样本数量对模型性能的影响：

```yaml
analysis:
  generated_count_analysis:
    enabled: true
    generated_counts: [50, 100, 200, 500, 1000]
```

### 3. 噪声鲁棒性分析

分析不同噪声水平对模型性能的影响：

```yaml
analysis:
  noise_analysis:
    enabled: true
    noise_levels: [0.0, 0.05, 0.1, 0.15, 0.2, 0.25]
```

### 4. 方法对比

对比不同数据增强方法的性能：

- CDDPM（主要方法）
- CGAN、WGAN、WGAN-GP、ACGAN（GAN系列）
- ADASYN、SMOTEENN（传统方法）

## 评估指标

### 分类指标

- **准确率 (Accuracy)**
- **精确率 (Precision)**
- **召回率 (Recall)**
- **F1分数 (F1-Score)**
- **混淆矩阵 (Confusion Matrix)**

### 生成质量指标

- **GAN-train**：用生成数据训练，在真实数据上测试
- **GAN-test**：用真实数据训练，在生成数据上测试

### 可视化

- **t-SNE降维可视化**：特征分布可视化
- **训练曲线**：损失和准确率变化
- **方法对比图**：不同方法性能对比

### 性能指标

- **训练时间**
- **GPU占用率**
- **内存使用量**
- **模型FLOPs**

## 结果保存

### 目录结构

```
results/
├── plots/                  # 可视化图片
├── batch_experiments/      # 批量实验结果
├── classifier_KAT_CDDPM.json  # 分类器结果
├── method_comparison.json  # 方法对比结果
└── comprehensive_test.json # 综合测试结果
```

### 生成样本保存

```
dataset/
└── KAT/
    └── gen_samples/
        ├── CDDPM/
        │   ├── generated_data.npy
        │   └── generated_labels.npy
        ├── CGAN/
        └── WGAN-GP/
```

## 常见问题

### 1. 内存不足

- 减小 `batch_size`
- 减少 `max_train_samples`
- 使用较小的 `truncate_length`

### 2. 训练速度慢

- 使用更强的GPU
- 启用混合精度训练：`mixed_precision: true`
- 减少 `num_epochs`

### 3. 生成样本质量差

- 增加生成器训练轮数
- 调整学习率
- 尝试不同的网络结构

### 4. 数据加载错误

- 检查数据文件路径
- 确认数据格式正确
- 查看数据集README说明

## 扩展功能

### 添加新的数据增强方法

1. 在 `models/` 目录下创建新的模型文件
2. 在 `common/trainer.py` 中添加对应的训练器
3. 在 `configs/config.yaml` 中添加配置
4. 在 `train.py` 中添加调用逻辑

### 添加新的数据集

1. 按照标准格式组织数据
2. 在 `configs/config.yaml` 中添加数据集信息
3. 更新 `common/data_loader.py` 中的支持列表

### 自定义评估指标

1. 在 `common/evaluation.py` 中添加新的指标计算函数
2. 在配置文件中启用新指标
3. 在可视化模块中添加对应的绘图函数

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 引用

如果您使用了本项目的代码，请引用相关论文：

```bibtex
@article{data_augmentation_fault_diagnosis,
  title={Data Augmentation Fault Diagnosis of Rolling Machinery Using Condition Denoising Diffusion Probabilistic Model and Improved CNN},
  author={...},
  journal={...},
  year={2024}
}
```

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交Issue到GitHub仓库
- 发送邮件到项目维护者

---

**注意**：本项目仅用于学术研究目的，请遵守相关数据使用协议和法律法规。
