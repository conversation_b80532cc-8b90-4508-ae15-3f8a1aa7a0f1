# KAT数据集示例配置文件
# 用于快速开始和测试

# 实验设置
experiment:
  name: "kat_fault_diagnosis_example"
  description: "KAT数据集故障诊断示例实验"
  seed: 42
  device: "cuda"  # cuda, cpu, auto
  mixed_precision: true

# 数据集配置
dataset:
  # 当前使用的数据集
  name: "KAT"
  
  # 数据集信息
  datasets:
    KAT:
      num_classes: 8
      signal_length: 1024
      train_samples: 1600
      test_samples: 1600
      data_type: "float64"
      label_type: "int32"
  
  # 数据加载参数
  data_loading:
    # 样本截取长度（从原始信号长度中截取）
    truncate_length: 512  # 使用512长度进行快速测试
    # 加载的样本数量限制（用于快速测试）
    max_train_samples: 800  # 每类100个样本
    max_test_samples: 400   # 每类50个样本
    # 数据预处理
    normalize: true
    standardize: false
    # 噪声添加
    add_noise: false
    noise_level: 0.1
    noise_type: "gaussian"
  
  # 数据增强配置
  augmentation:
    enabled: true
    generated_samples_per_class: 100  # 每类生成100个样本
    methods: ["CDDPM", "CGAN", "ADASYN"]  # 测试三种方法
    current_method: "CDDPM"

# 模型配置
models:
  # 分类器配置 (MR-CNN)
  classifier:
    name: "MRCNN"
    kernel_sizes: [3, 5, 7]
    base_channels: 64
    dropout: 0.1
    
  # 生成模型配置
  generators:
    # CDDPM配置（简化版本用于快速测试）
    CDDPM:
      model_type: "unet1d"
      hidden_dim: 64      # 减小模型大小
      num_layers: 3       # 减少层数
      num_heads: 4        # 减少注意力头数
      time_steps: 500     # 减少时间步数
      beta_schedule: "cosine"
      loss_type: "mse"
      
    # CGAN配置
    CGAN:
      generator:
        noise_dim: 100
        hidden_dims: [128, 256, 512]  # 减小网络
        output_activation: "tanh"
      discriminator:
        hidden_dims: [512, 256, 128]  # 减小网络
        dropout: 0.3
        
    # 传统方法配置
    ADASYN:
      n_neighbors: 5
      random_state: 42

# 训练配置
training:
  # 通用训练参数
  batch_size: 32
  num_epochs: 50        # 减少训练轮数用于快速测试
  learning_rate: 0.001
  weight_decay: 1e-4
  
  # 优化器配置
  optimizer:
    type: "Adam"
    betas: [0.9, 0.999]
    eps: 1e-8
    
  # 学习率调度
  scheduler:
    type: "CosineAnnealingLR"
    T_max: 50
    eta_min: 1e-6
    
  # 早停配置
  early_stopping:
    enabled: true
    patience: 10        # 减少耐心值
    min_delta: 1e-4
    monitor: "val_accuracy"
    mode: "max"
    
  # 检查点保存
  checkpoint:
    save_best: true
    save_process: true
    process_intervals: 10
    save_frequency: 10   # 每10个epoch保存一次
    
  # 生成模型特定训练参数
  generator_training:
    # CDDPM训练参数
    CDDPM:
      num_epochs: 100    # 减少训练轮数
      learning_rate: 1e-4
      batch_size: 64
      
    # GAN系列训练参数
    GAN:
      num_epochs: 100    # 减少训练轮数
      g_learning_rate: 2e-4
      d_learning_rate: 2e-4
      batch_size: 64
      n_critic: 5

# 评估配置
evaluation:
  # 评估指标
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "confusion_matrix"
    
  # 可视化配置
  visualization:
    tsne:
      enabled: true
      perplexity: 30
      n_iter: 1000
      random_state: 42
    confusion_matrix:
      enabled: true
      normalize: true
    
  # 计算性能指标
  performance_metrics:
    measure_time: true
    measure_gpu_usage: true
    measure_flops: false  # 关闭FLOPs计算以加快速度
    
  # 交叉验证（暂时禁用）
  cross_validation:
    enabled: false
    n_folds: 5
    random_state: 42

# 实验分析配置（简化版本）
analysis:
  # 原始样本数量分析
  sample_count_analysis:
    enabled: true
    sample_ratios: [0.2, 0.5, 1.0]  # 简化测试
    
  # 生成样本数量分析
  generated_count_analysis:
    enabled: true
    generated_counts: [50, 100, 200]  # 简化测试
    
  # 噪声鲁棒性分析
  noise_analysis:
    enabled: true
    noise_levels: [0.0, 0.1, 0.2]  # 简化测试
    
  # 消融实验
  ablation_study:
    enabled: false  # 暂时禁用

# 批量实验配置
batch_experiments:
  # 组合实验
  combinations:
    # 实验1：样本数量 + 生成方法
    exp1:
      name: "sample_count_vs_methods"
      sample_ratios: [0.5, 1.0]
      methods: ["CDDPM", "CGAN"]

# 输出配置
output:
  # 结果保存
  save_results: true
  save_models: true
  save_generated_samples: true
  
  # 日志配置
  logging:
    level: "INFO"
    save_to_file: true
    console_output: true
    
  # 可视化保存
  save_plots: true
  plot_format: "png"
  plot_dpi: 300
