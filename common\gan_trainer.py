"""
GAN系列模型训练器
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
from typing import Dict, Any, Tuple
import logging
from tqdm import tqdm

from .trainer import GeneratorTrainer
from .evaluation import PerformanceMonitor

logger = logging.getLogger(__name__)


class GANTrainer(GeneratorTrainer):
    """GAN训练器基类"""
    
    def __init__(
        self,
        model: nn.Module,
        config: Dict[str, Any],
        device: torch.device,
        save_dir: str = "checkpoints",
        model_type: str = "CGAN"
    ):
        super().__init__(model, config, device, save_dir)
        
        self.model_type = model_type
        
        # GAN特定配置
        gan_config = config['training']['generator_training']['GAN']
        
        # 优化器
        self.g_optimizer = optim.Adam(
            model.generator.parameters(),
            lr=gan_config['g_learning_rate'],
            betas=[0.5, 0.999]
        )
        
        self.d_optimizer = optim.Adam(
            model.discriminator.parameters(),
            lr=gan_config['d_learning_rate'],
            betas=[0.5, 0.999]
        )
        
        # 损失函数
        self.adversarial_loss = nn.BCEWithLogitsLoss()
        self.classification_loss = nn.CrossEntropyLoss()
        
        self.num_epochs = gan_config['num_epochs']
        self.batch_size = gan_config['batch_size']
        self.n_critic = gan_config.get('n_critic', 5)
        
        # 特定模型参数
        if model_type == "WGAN":
            self.clip_value = config['models']['generators']['WGAN'].get('clip_value', 0.01)
        elif model_type == "WGAN_GP":
            self.lambda_gp = config['models']['generators']['WGAN_GP'].get('lambda_gp', 10.0)
        elif model_type == "ACGAN":
            self.lambda_cls = config['models']['generators']['ACGAN'].get('lambda_cls', 1.0)
    
    def train_cgan(self, train_loader: DataLoader) -> Dict[str, Any]:
        """训练CGAN"""
        logger.info(f"开始训练CGAN，共{self.num_epochs}个epoch")
        
        g_losses = []
        d_losses = []
        
        for epoch in range(self.num_epochs):
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            num_batches = 0
            
            pbar = tqdm(train_loader, desc=f"CGAN Epoch {epoch+1}/{self.num_epochs}")
            for batch_idx, (real_data, real_labels) in enumerate(pbar):
                batch_size = real_data.size(0)
                real_data, real_labels = real_data.to(self.device), real_labels.to(self.device)
                
                # 确保数据格式正确
                if real_data.dim() == 3:
                    real_data = real_data.squeeze(1)  # [B, L]
                
                # 真假标签
                real_label = torch.ones(batch_size, 1, device=self.device)
                fake_label = torch.zeros(batch_size, 1, device=self.device)
                
                # 训练判别器
                self.d_optimizer.zero_grad()
                
                # 真实样本
                real_output, _ = self.model.discriminator(real_data, real_labels)
                d_real_loss = self.adversarial_loss(real_output, real_label)
                
                # 生成样本
                noise = torch.randn(batch_size, self.model.noise_dim, device=self.device)
                fake_data = self.model.generator(noise, real_labels)
                fake_output, _ = self.model.discriminator(fake_data.detach(), real_labels)
                d_fake_loss = self.adversarial_loss(fake_output, fake_label)
                
                d_loss = d_real_loss + d_fake_loss
                d_loss.backward()
                self.d_optimizer.step()
                
                # 训练生成器
                self.g_optimizer.zero_grad()
                
                fake_output, _ = self.model.discriminator(fake_data, real_labels)
                g_loss = self.adversarial_loss(fake_output, real_label)
                
                g_loss.backward()
                self.g_optimizer.step()
                
                epoch_g_loss += g_loss.item()
                epoch_d_loss += d_loss.item()
                num_batches += 1
                
                pbar.set_postfix({
                    'G_Loss': f'{g_loss.item():.4f}',
                    'D_Loss': f'{d_loss.item():.4f}'
                })
                
                if batch_idx % 10 == 0:
                    self.performance_monitor.record_usage()
            
            avg_g_loss = epoch_g_loss / num_batches
            avg_d_loss = epoch_d_loss / num_batches
            
            g_losses.append(avg_g_loss)
            d_losses.append(avg_d_loss)
            
            logger.info(f"CGAN Epoch {epoch+1}/{self.num_epochs}, G_Loss: {avg_g_loss:.4f}, D_Loss: {avg_d_loss:.4f}")
            
            # 保存模型
            if (epoch + 1) % 50 == 0:
                self.save_model("cgan", epoch + 1)
        
        return {'g_losses': g_losses, 'd_losses': d_losses}
    
    def train_wgan(self, train_loader: DataLoader) -> Dict[str, Any]:
        """训练WGAN"""
        logger.info(f"开始训练WGAN，共{self.num_epochs}个epoch")
        
        g_losses = []
        d_losses = []
        
        for epoch in range(self.num_epochs):
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            num_batches = 0
            
            pbar = tqdm(train_loader, desc=f"WGAN Epoch {epoch+1}/{self.num_epochs}")
            for batch_idx, (real_data, real_labels) in enumerate(pbar):
                batch_size = real_data.size(0)
                real_data, real_labels = real_data.to(self.device), real_labels.to(self.device)
                
                if real_data.dim() == 3:
                    real_data = real_data.squeeze(1)
                
                # 训练判别器（critic）
                for _ in range(self.n_critic):
                    self.d_optimizer.zero_grad()
                    
                    # 真实样本
                    real_output, _ = self.model.discriminator(real_data, real_labels)
                    
                    # 生成样本
                    noise = torch.randn(batch_size, self.model.noise_dim, device=self.device)
                    fake_data = self.model.generator(noise, real_labels)
                    fake_output, _ = self.model.discriminator(fake_data.detach(), real_labels)
                    
                    # Wasserstein损失
                    d_loss = -torch.mean(real_output) + torch.mean(fake_output)
                    d_loss.backward()
                    self.d_optimizer.step()
                    
                    # 权重裁剪
                    self.model.clip_discriminator_weights()
                
                # 训练生成器
                self.g_optimizer.zero_grad()
                
                noise = torch.randn(batch_size, self.model.noise_dim, device=self.device)
                fake_data = self.model.generator(noise, real_labels)
                fake_output, _ = self.model.discriminator(fake_data, real_labels)
                
                g_loss = -torch.mean(fake_output)
                g_loss.backward()
                self.g_optimizer.step()
                
                epoch_g_loss += g_loss.item()
                epoch_d_loss += d_loss.item()
                num_batches += 1
                
                pbar.set_postfix({
                    'G_Loss': f'{g_loss.item():.4f}',
                    'D_Loss': f'{d_loss.item():.4f}'
                })
            
            avg_g_loss = epoch_g_loss / num_batches
            avg_d_loss = epoch_d_loss / num_batches
            
            g_losses.append(avg_g_loss)
            d_losses.append(avg_d_loss)
            
            logger.info(f"WGAN Epoch {epoch+1}/{self.num_epochs}, G_Loss: {avg_g_loss:.4f}, D_Loss: {avg_d_loss:.4f}")
        
        return {'g_losses': g_losses, 'd_losses': d_losses}
    
    def train_wgan_gp(self, train_loader: DataLoader) -> Dict[str, Any]:
        """训练WGAN-GP"""
        logger.info(f"开始训练WGAN-GP，共{self.num_epochs}个epoch")
        
        g_losses = []
        d_losses = []
        gp_losses = []
        
        for epoch in range(self.num_epochs):
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            epoch_gp_loss = 0.0
            num_batches = 0
            
            pbar = tqdm(train_loader, desc=f"WGAN-GP Epoch {epoch+1}/{self.num_epochs}")
            for batch_idx, (real_data, real_labels) in enumerate(pbar):
                batch_size = real_data.size(0)
                real_data, real_labels = real_data.to(self.device), real_labels.to(self.device)
                
                if real_data.dim() == 3:
                    real_data = real_data.squeeze(1)
                
                # 训练判别器
                for _ in range(self.n_critic):
                    self.d_optimizer.zero_grad()
                    
                    # 真实样本
                    real_output, _ = self.model.discriminator(real_data, real_labels)
                    
                    # 生成样本
                    noise = torch.randn(batch_size, self.model.noise_dim, device=self.device)
                    fake_data = self.model.generator(noise, real_labels)
                    fake_output, _ = self.model.discriminator(fake_data.detach(), real_labels)
                    
                    # 梯度惩罚
                    gp = self.model.gradient_penalty(
                        real_data.unsqueeze(1), fake_data, real_labels, self.device
                    )
                    
                    # 总损失
                    d_loss = -torch.mean(real_output) + torch.mean(fake_output) + self.lambda_gp * gp
                    d_loss.backward()
                    self.d_optimizer.step()
                
                # 训练生成器
                self.g_optimizer.zero_grad()
                
                noise = torch.randn(batch_size, self.model.noise_dim, device=self.device)
                fake_data = self.model.generator(noise, real_labels)
                fake_output, _ = self.model.discriminator(fake_data, real_labels)
                
                g_loss = -torch.mean(fake_output)
                g_loss.backward()
                self.g_optimizer.step()
                
                epoch_g_loss += g_loss.item()
                epoch_d_loss += d_loss.item()
                epoch_gp_loss += gp.item()
                num_batches += 1
                
                pbar.set_postfix({
                    'G_Loss': f'{g_loss.item():.4f}',
                    'D_Loss': f'{d_loss.item():.4f}',
                    'GP': f'{gp.item():.4f}'
                })
            
            avg_g_loss = epoch_g_loss / num_batches
            avg_d_loss = epoch_d_loss / num_batches
            avg_gp_loss = epoch_gp_loss / num_batches
            
            g_losses.append(avg_g_loss)
            d_losses.append(avg_d_loss)
            gp_losses.append(avg_gp_loss)
            
            logger.info(f"WGAN-GP Epoch {epoch+1}/{self.num_epochs}, G_Loss: {avg_g_loss:.4f}, D_Loss: {avg_d_loss:.4f}, GP: {avg_gp_loss:.4f}")
        
        return {'g_losses': g_losses, 'd_losses': d_losses, 'gp_losses': gp_losses}
    
    def train_acgan(self, train_loader: DataLoader) -> Dict[str, Any]:
        """训练ACGAN"""
        logger.info(f"开始训练ACGAN，共{self.num_epochs}个epoch")
        
        g_losses = []
        d_losses = []
        
        for epoch in range(self.num_epochs):
            epoch_g_loss = 0.0
            epoch_d_loss = 0.0
            num_batches = 0
            
            pbar = tqdm(train_loader, desc=f"ACGAN Epoch {epoch+1}/{self.num_epochs}")
            for batch_idx, (real_data, real_labels) in enumerate(pbar):
                batch_size = real_data.size(0)
                real_data, real_labels = real_data.to(self.device), real_labels.to(self.device)
                
                if real_data.dim() == 3:
                    real_data = real_data.squeeze(1)
                
                # 真假标签
                real_label = torch.ones(batch_size, 1, device=self.device)
                fake_label = torch.zeros(batch_size, 1, device=self.device)
                
                # 训练判别器
                self.d_optimizer.zero_grad()
                
                # 真实样本
                real_adv_output, real_cls_output = self.model.discriminator(real_data)
                d_real_adv_loss = self.adversarial_loss(real_adv_output, real_label)
                d_real_cls_loss = self.classification_loss(real_cls_output, real_labels)
                
                # 生成样本
                noise = torch.randn(batch_size, self.model.noise_dim, device=self.device)
                fake_data = self.model.generator(noise, real_labels)
                fake_adv_output, fake_cls_output = self.model.discriminator(fake_data.detach())
                d_fake_adv_loss = self.adversarial_loss(fake_adv_output, fake_label)
                d_fake_cls_loss = self.classification_loss(fake_cls_output, real_labels)
                
                d_loss = (d_real_adv_loss + d_fake_adv_loss + 
                         self.lambda_cls * (d_real_cls_loss + d_fake_cls_loss))
                d_loss.backward()
                self.d_optimizer.step()
                
                # 训练生成器
                self.g_optimizer.zero_grad()
                
                fake_adv_output, fake_cls_output = self.model.discriminator(fake_data)
                g_adv_loss = self.adversarial_loss(fake_adv_output, real_label)
                g_cls_loss = self.classification_loss(fake_cls_output, real_labels)
                
                g_loss = g_adv_loss + self.lambda_cls * g_cls_loss
                g_loss.backward()
                self.g_optimizer.step()
                
                epoch_g_loss += g_loss.item()
                epoch_d_loss += d_loss.item()
                num_batches += 1
                
                pbar.set_postfix({
                    'G_Loss': f'{g_loss.item():.4f}',
                    'D_Loss': f'{d_loss.item():.4f}'
                })
            
            avg_g_loss = epoch_g_loss / num_batches
            avg_d_loss = epoch_d_loss / num_batches
            
            g_losses.append(avg_g_loss)
            d_losses.append(avg_d_loss)
            
            logger.info(f"ACGAN Epoch {epoch+1}/{self.num_epochs}, G_Loss: {avg_g_loss:.4f}, D_Loss: {avg_d_loss:.4f}")
        
        return {'g_losses': g_losses, 'd_losses': d_losses}
    
    def train(self, train_loader: DataLoader) -> Dict[str, Any]:
        """训练GAN模型"""
        self.performance_monitor.start_monitoring()
        
        if self.model_type == "CGAN":
            results = self.train_cgan(train_loader)
        elif self.model_type == "WGAN":
            results = self.train_wgan(train_loader)
        elif self.model_type == "WGAN_GP":
            results = self.train_wgan_gp(train_loader)
        elif self.model_type == "ACGAN":
            results = self.train_acgan(train_loader)
        else:
            raise ValueError(f"不支持的GAN类型: {self.model_type}")
        
        # 保存最终模型
        self.save_model(self.model_type.lower())
        
        # 获取性能指标
        performance_metrics = self.performance_monitor.get_performance_metrics()
        results['performance_metrics'] = performance_metrics
        
        logger.info(f"{self.model_type}训练完成")
        return results
    
    def generate_samples(
        self,
        num_samples_per_class: int,
        num_classes: int,
        signal_length: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """生成GAN样本"""
        self.model.eval()
        
        generated_data = []
        generated_labels = []
        
        with torch.no_grad():
            for class_id in range(num_classes):
                # 创建类别标签
                labels = torch.full((num_samples_per_class,), class_id, device=self.device)
                
                # 生成样本
                samples = self.model.generate(num_samples_per_class, labels, self.device)
                
                generated_data.append(samples.cpu().numpy())
                generated_labels.extend([class_id] * num_samples_per_class)
        
        generated_data = np.vstack(generated_data)
        generated_labels = np.array(generated_labels)
        
        logger.info(f"{self.model_type}生成样本: {generated_data.shape}")
        return generated_data, generated_labels
