#!/usr/bin/env python3
"""
快速测试脚本
用于验证项目是否正确安装和配置
"""

import os
import sys
import yaml
import torch
import numpy as np
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """设置简单的日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'logs/quick_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )

def test_imports():
    """测试所有必要的导入"""
    logger = logging.getLogger(__name__)
    logger.info("测试模块导入...")
    
    try:
        # 测试基础库
        import torch
        import numpy as np
        import yaml
        import matplotlib.pyplot as plt
        import seaborn as sns
        import sklearn
        logger.info("✓ 基础库导入成功")
        
        # 测试项目模块
        from common.data_loader import DataLoaderManager
        from common.trainer import ClassifierTrainer
        from common.evaluation import MetricsCalculator, Visualizer
        from models.mr_cnn import MRCNN
        from models.cddpm import CDDPM
        from models.gan_models import CGAN
        from models.traditional_augmentation import ADASYN
        logger.info("✓ 项目模块导入成功")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ 导入失败: {e}")
        return False

def test_config():
    """测试配置文件加载"""
    logger = logging.getLogger(__name__)
    logger.info("测试配置文件...")
    
    try:
        config_path = "configs/config_kat_example.yaml"
        if not os.path.exists(config_path):
            logger.error(f"✗ 配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必要的配置项
        required_keys = ['experiment', 'dataset', 'models', 'training']
        for key in required_keys:
            if key not in config:
                logger.error(f"✗ 配置文件缺少必要项: {key}")
                return False
        
        logger.info("✓ 配置文件加载成功")
        return True, config
        
    except Exception as e:
        logger.error(f"✗ 配置文件加载失败: {e}")
        return False, None

def test_data_loading():
    """测试数据加载"""
    logger = logging.getLogger(__name__)
    logger.info("测试数据加载...")
    
    try:
        # 检查数据集目录
        dataset_dirs = ["dataset/KAT", "dataset/SK", "dataset/JST"]
        available_datasets = []
        
        for dataset_dir in dataset_dirs:
            if os.path.exists(dataset_dir):
                dataset_name = os.path.basename(dataset_dir)
                
                # 检查训练数据
                train_data_path = os.path.join(dataset_dir, "used_npy", "train", f"{dataset_name.lower()}_data.npy")
                train_label_path = os.path.join(dataset_dir, "used_npy", "train", f"{dataset_name.lower()}_label.npy")
                
                if os.path.exists(train_data_path) and os.path.exists(train_label_path):
                    available_datasets.append(dataset_name)
                    logger.info(f"✓ 找到数据集: {dataset_name}")
        
        if not available_datasets:
            logger.warning("✗ 未找到可用的数据集")
            return False, None
        
        # 测试加载第一个可用数据集
        test_dataset = available_datasets[0]
        logger.info(f"测试加载数据集: {test_dataset}")
        
        # 创建简单配置
        config = {
            'dataset': {
                'name': test_dataset,
                'datasets': {
                    test_dataset: {
                        'num_classes': 8 if test_dataset == 'KAT' else 3,
                        'signal_length': 1024
                    }
                },
                'data_loading': {
                    'truncate_length': 512,
                    'max_train_samples': 100,
                    'max_test_samples': 50,
                    'normalize': True,
                    'standardize': False,
                    'add_noise': False
                }
            }
        }
        
        from common.data_loader import DataLoaderManager
        data_manager = DataLoaderManager(config)
        
        # 加载少量数据进行测试
        train_data, train_labels = data_manager.load_dataset(test_dataset, "train")
        logger.info(f"✓ 训练数据加载成功: {train_data.shape}, 标签: {train_labels.shape}")
        
        # 创建数据加载器
        train_loader = data_manager.create_dataloader(test_dataset, "train", batch_size=16)
        logger.info(f"✓ 数据加载器创建成功")
        
        return True, (test_dataset, config)
        
    except Exception as e:
        logger.error(f"✗ 数据加载测试失败: {e}")
        return False, None

def test_model_creation():
    """测试模型创建"""
    logger = logging.getLogger(__name__)
    logger.info("测试模型创建...")
    
    try:
        # 测试设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {device}")
        
        # 创建简单配置
        config = {
            'dataset': {
                'name': 'KAT',
                'datasets': {
                    'KAT': {
                        'num_classes': 8,
                        'signal_length': 1024
                    }
                }
            },
            'models': {
                'classifier': {
                    'kernel_sizes': [3, 5, 7],
                    'base_channels': 32  # 使用较小的网络进行测试
                }
            }
        }
        
        # 测试MR-CNN
        from models.mr_cnn import MRCNN
        model = MRCNN(config=config, num_classes=8, input_channels=1)
        model = model.to(device)
        logger.info(f"✓ MR-CNN模型创建成功")
        
        # 测试前向传播
        test_input = torch.randn(4, 1, 512).to(device)
        with torch.no_grad():
            output = model(test_input)
        logger.info(f"✓ MR-CNN前向传播成功: {output.shape}")
        
        # 测试CDDPM（简化版本）
        from models.cddpm import CDDPM
        cddpm_model = CDDPM(
            signal_length=512,
            num_classes=8,
            num_timesteps=100,  # 减少时间步数
            hidden_dim=32,      # 减小网络
            num_layers=2        # 减少层数
        )
        cddpm_model = cddpm_model.to(device)
        logger.info(f"✓ CDDPM模型创建成功")
        
        return True, device
        
    except Exception as e:
        logger.error(f"✗ 模型创建测试失败: {e}")
        return False, None

def test_training_step():
    """测试训练步骤"""
    logger = logging.getLogger(__name__)
    logger.info("测试训练步骤...")
    
    try:
        # 创建虚拟数据
        batch_size = 8
        signal_length = 512
        num_classes = 8
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        config = {
            'dataset': {
                'name': 'KAT',
                'datasets': {
                    'KAT': {
                        'num_classes': num_classes,
                        'signal_length': signal_length
                    }
                }
            },
            'models': {
                'classifier': {
                    'kernel_sizes': [3, 5, 7],
                    'base_channels': 32
                }
            },
            'training': {
                'batch_size': batch_size,
                'learning_rate': 0.001,
                'weight_decay': 1e-4,
                'optimizer': {
                    'type': 'Adam',
                    'betas': [0.9, 0.999],
                    'eps': 1e-8
                },
                'scheduler': {
                    'type': 'CosineAnnealingLR',
                    'T_max': 10
                },
                'early_stopping': {
                    'enabled': False
                },
                'checkpoint': {
                    'save_best': False,
                    'save_process': False
                }
            }
        }
        
        from models.mr_cnn import MRCNN
        model = MRCNN(config=config, num_classes=num_classes, input_channels=1)
        
        # 创建虚拟数据
        train_data = torch.randn(batch_size * 2, signal_length)
        train_labels = torch.randint(0, num_classes, (batch_size * 2,))
        
        from common.data_loader import FaultDiagnosisDataset
        from torch.utils.data import DataLoader
        
        train_dataset = FaultDiagnosisDataset(train_data.numpy(), train_labels.numpy())
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        val_dataset = FaultDiagnosisDataset(train_data.numpy(), train_labels.numpy())
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        
        # 创建训练器
        from common.trainer import ClassifierTrainer
        trainer = ClassifierTrainer(model, config, device, save_dir="test_checkpoints")
        
        # 测试一个训练epoch
        train_loss, train_acc = trainer.train_epoch(train_loader)
        logger.info(f"✓ 训练epoch测试成功: loss={train_loss:.4f}, acc={train_acc:.4f}")
        
        # 测试验证
        val_loss, val_acc = trainer.validate_epoch(val_loader)
        logger.info(f"✓ 验证epoch测试成功: loss={val_loss:.4f}, acc={val_acc:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 训练步骤测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("故障诊断数据增强项目 - 快速测试")
    print("=" * 60)
    
    # 创建必要目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("test_checkpoints", exist_ok=True)
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始快速测试...")
    
    # 测试步骤
    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config),
        ("数据加载", test_data_loading),
        ("模型创建", test_model_creation),
        ("训练步骤", test_training_step)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if isinstance(result, tuple):
                success = result[0]
            else:
                success = result
            
            results[test_name] = success
            
            if success:
                logger.info(f"✓ {test_name} 测试通过")
            else:
                logger.error(f"✗ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"✗ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 总结
    logger.info(f"\n{'='*20} 测试总结 {'='*20}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✓ 通过" if success else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！项目配置正确。")
        logger.info("\n下一步:")
        logger.info("1. 运行 'python train.py --config configs/config_kat_example.yaml --mode all' 开始完整训练")
        logger.info("2. 或运行 'python batch_train.py --config configs/config_kat_example.yaml' 进行批量实验")
    else:
        logger.error("❌ 部分测试失败，请检查配置和依赖。")
        logger.info("\n建议:")
        logger.info("1. 检查是否安装了所有依赖: pip install -r requirements.txt")
        logger.info("2. 检查数据集是否正确放置在 dataset/ 目录下")
        logger.info("3. 检查CUDA环境是否正确配置")
    
    # 清理测试文件
    import shutil
    if os.path.exists("test_checkpoints"):
        shutil.rmtree("test_checkpoints")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
