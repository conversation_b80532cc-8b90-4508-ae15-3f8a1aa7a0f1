MAFAULDA数据集 - NPY格式数据说明
==============================

数据格式说明:
- NPY文件格式: 
  * 训练数据: mafaulda_data.npy (样本矩阵), mafaulda_label.npy (整数类型标签)
  * 测试数据: mafaulda_data.npy (样本矩阵), mafaulda_label.npy (整数类型标签)

标签说明:
- 0: data
- 1: 20g_data
- 2: 35g_data
- 3: 6g_data
- 4: 0.50_data
- 5: 2.00_data
- 6: 0.51_data
- 7: 1.27_data
- 8: 1.90_data
- 9: ball_fault_20g_data
- 10: cage_fault_20g_data
- 11: outer_race_20g_data
- 12: ball_fault_20g_data
- 13: cage_fault_20g_data
- 14: outer_race_20g_data

样本数量:
- 训练集: 每个类别约 200 个样本 (总计约 3000 个样本)
- 测试集: 每个类别约 200 个样本 (总计约 3000 个样本)

采样方法:
- 从每个类别的每个文件中随机提取样本，允许样本重叠
- 每个样本包含 1024 个数据点
