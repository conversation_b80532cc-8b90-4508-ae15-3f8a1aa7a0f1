#!/usr/bin/env python3
"""
测试脚本
用于测试训练好的模型
"""

import os
import sys
import yaml
import torch
import numpy as np
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.data_loader import DataLoaderManager
from common.trainer import ClassifierTrainer
from common.evaluation import MetricsCalculator, Visualizer, ResultsManager, PerformanceMonitor
from models.mr_cnn import MRCNN
from train import setup_logging, load_config, set_seed, get_device, create_model


class ModelTester:
    """模型测试器"""
    
    def __init__(self, config: Dict[str, Any], device: torch.device):
        """
        初始化模型测试器
        
        Args:
            config: 配置字典
            device: 设备
        """
        self.config = config
        self.device = device
        self.logger = logging.getLogger(__name__)
        
        # 创建数据加载器管理器
        self.data_manager = DataLoaderManager(config)
        
        # 创建结果管理器和可视化器
        self.results_manager = ResultsManager()
        self.visualizer = Visualizer()
        
        # 性能监控器
        self.performance_monitor = PerformanceMonitor()
    
    def load_model(self, checkpoint_path: str) -> MRCNN:
        """
        加载训练好的模型
        
        Args:
            checkpoint_path: 检查点路径
            
        Returns:
            加载的模型
        """
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")
        
        # 创建模型
        model = create_model(self.config, self.device)
        
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        self.logger.info(f"模型加载成功: {checkpoint_path}")
        
        return model
    
    def test_model(
        self,
        model: MRCNN,
        test_dataset: str = None,
        use_augmented_data: bool = False,
        augmentation_method: str = None
    ) -> Dict[str, Any]:
        """
        测试模型
        
        Args:
            model: 要测试的模型
            test_dataset: 测试数据集名称（如果为None则使用配置中的数据集）
            use_augmented_data: 是否使用增强数据进行测试
            augmentation_method: 增强方法名称
            
        Returns:
            测试结果
        """
        self.logger.info("开始模型测试")
        
        # 确定测试数据集
        if test_dataset is None:
            test_dataset = self.config['dataset']['name']
        
        # 开始性能监控
        self.performance_monitor.start_monitoring()
        
        # 创建测试数据加载器
        if use_augmented_data and augmentation_method:
            # 使用增强数据测试
            try:
                generated_data, generated_labels = self.data_manager.load_generated_samples(
                    dataset_name=test_dataset,
                    method_name=augmentation_method
                )
                
                from common.data_loader import FaultDiagnosisDataset
                from torch.utils.data import DataLoader
                
                test_dataset_obj = FaultDiagnosisDataset(generated_data, generated_labels)
                test_loader = DataLoader(
                    test_dataset_obj,
                    batch_size=self.config['training']['batch_size'],
                    shuffle=False,
                    num_workers=0
                )
                
                self.logger.info(f"使用增强数据测试，方法: {augmentation_method}")
                
            except FileNotFoundError:
                self.logger.warning(f"未找到增强数据，使用原始测试数据")
                test_loader = self.data_manager.create_dataloader(
                    dataset_name=test_dataset,
                    data_type="test",
                    shuffle=False
                )
        else:
            # 使用原始测试数据
            test_loader = self.data_manager.create_dataloader(
                dataset_name=test_dataset,
                data_type="test",
                shuffle=False
            )
        
        # 创建训练器（用于评估）
        trainer = ClassifierTrainer(model, self.config, self.device)
        
        # 评估模型
        test_acc, y_true, y_pred = trainer.evaluate(test_loader)
        
        # 计算详细指标
        dataset_info = self.config['dataset']['datasets'][test_dataset]
        metrics_calculator = MetricsCalculator(
            num_classes=dataset_info['num_classes']
        )
        
        metrics = metrics_calculator.calculate_basic_metrics(y_true, y_pred)
        confusion_matrix = metrics_calculator.calculate_confusion_matrix(y_true, y_pred)
        
        # 提取特征用于t-SNE
        features, labels = trainer.extract_features(test_loader)
        
        # 获取性能指标
        performance_metrics = self.performance_monitor.get_performance_metrics()
        
        # 整合结果
        results = {
            'test_accuracy': test_acc,
            'metrics': metrics,
            'confusion_matrix': confusion_matrix.tolist(),
            'predictions': {
                'y_true': y_true.tolist(),
                'y_pred': y_pred.tolist()
            },
            'features': features.tolist(),
            'labels': labels.tolist(),
            'performance_metrics': performance_metrics,
            'test_info': {
                'dataset': test_dataset,
                'use_augmented_data': use_augmented_data,
                'augmentation_method': augmentation_method,
                'num_samples': len(y_true)
            }
        }
        
        self.logger.info(f"模型测试完成，准确率: {test_acc:.4f}")
        
        return results
    
    def calculate_gan_metrics(
        self,
        model: MRCNN,
        dataset_name: str,
        augmentation_method: str
    ) -> Dict[str, float]:
        """
        计算GAN-train和GAN-test指标
        
        Args:
            model: 分类模型
            dataset_name: 数据集名称
            augmentation_method: 增强方法名称
            
        Returns:
            GAN指标
        """
        self.logger.info(f"计算GAN指标，方法: {augmentation_method}")
        
        try:
            # 加载真实数据
            real_train_loader = self.data_manager.create_dataloader(
                dataset_name=dataset_name,
                data_type="train",
                shuffle=True
            )
            
            real_test_loader = self.data_manager.create_dataloader(
                dataset_name=dataset_name,
                data_type="test",
                shuffle=False
            )
            
            # 加载生成数据
            generated_data, generated_labels = self.data_manager.load_generated_samples(
                dataset_name=dataset_name,
                method_name=augmentation_method
            )
            
            # 计算GAN指标
            metrics_calculator = MetricsCalculator(
                num_classes=self.config['dataset']['datasets'][dataset_name]['num_classes']
            )
            
            gan_metrics = metrics_calculator.calculate_gan_metrics(
                model=model,
                real_train_loader=real_train_loader,
                real_test_loader=real_test_loader,
                generated_data=generated_data,
                generated_labels=generated_labels,
                device=self.device
            )
            
            self.logger.info(f"GAN指标计算完成: {gan_metrics}")
            
            return gan_metrics
            
        except Exception as e:
            self.logger.error(f"计算GAN指标失败: {e}")
            return {}
    
    def visualize_results(
        self,
        results: Dict[str, Any],
        save_prefix: str = "test"
    ):
        """
        可视化测试结果
        
        Args:
            results: 测试结果
            save_prefix: 保存文件前缀
        """
        self.logger.info("生成可视化结果")
        
        test_info = results['test_info']
        dataset_name = test_info['dataset']
        dataset_info = self.config['dataset']['datasets'][dataset_name]
        class_names = [f"Class_{i}" for i in range(dataset_info['num_classes'])]
        
        # 混淆矩阵
        confusion_matrix = np.array(results['confusion_matrix'])
        self.visualizer.plot_confusion_matrix(
            confusion_matrix,
            class_names,
            title=f"Confusion Matrix - {dataset_name}",
            save_name=f"{save_prefix}_confusion_matrix_{dataset_name}"
        )
        
        # t-SNE可视化
        features = np.array(results['features'])
        labels = np.array(results['labels'])
        
        self.visualizer.plot_tsne(
            features,
            labels,
            class_names,
            title=f"t-SNE Visualization - {dataset_name}",
            save_name=f"{save_prefix}_tsne_{dataset_name}"
        )
        
        self.logger.info("可视化结果生成完成")
    
    def run_comprehensive_test(
        self,
        checkpoint_path: str,
        test_datasets: Optional[List[str]] = None,
        test_augmentation_methods: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        运行综合测试
        
        Args:
            checkpoint_path: 模型检查点路径
            test_datasets: 测试数据集列表
            test_augmentation_methods: 测试增强方法列表
            
        Returns:
            综合测试结果
        """
        self.logger.info("开始综合测试")
        
        # 加载模型
        model = self.load_model(checkpoint_path)
        
        # 确定测试数据集
        if test_datasets is None:
            test_datasets = [self.config['dataset']['name']]
        
        # 确定测试增强方法
        if test_augmentation_methods is None:
            test_augmentation_methods = self.config['dataset']['augmentation']['methods']
        
        all_results = {}
        
        # 测试原始数据
        for dataset in test_datasets:
            self.logger.info(f"测试数据集: {dataset} (原始数据)")
            
            try:
                # 临时修改配置
                original_dataset = self.config['dataset']['name']
                self.config['dataset']['name'] = dataset
                
                results = self.test_model(model, dataset, use_augmented_data=False)
                all_results[f"{dataset}_original"] = results
                
                # 可视化
                self.visualize_results(results, f"test_{dataset}_original")
                
                # 恢复配置
                self.config['dataset']['name'] = original_dataset
                
            except Exception as e:
                self.logger.error(f"测试数据集 {dataset} 失败: {e}")
                all_results[f"{dataset}_original"] = {'error': str(e)}
        
        # 测试增强数据
        for dataset in test_datasets:
            for method in test_augmentation_methods:
                test_key = f"{dataset}_{method}"
                self.logger.info(f"测试: {test_key}")
                
                try:
                    # 临时修改配置
                    original_dataset = self.config['dataset']['name']
                    self.config['dataset']['name'] = dataset
                    
                    # 测试模型
                    results = self.test_model(
                        model, dataset, 
                        use_augmented_data=True, 
                        augmentation_method=method
                    )
                    
                    # 计算GAN指标
                    gan_metrics = self.calculate_gan_metrics(model, dataset, method)
                    results['gan_metrics'] = gan_metrics
                    
                    all_results[test_key] = results
                    
                    # 可视化
                    self.visualize_results(results, f"test_{test_key}")
                    
                    # 恢复配置
                    self.config['dataset']['name'] = original_dataset
                    
                except Exception as e:
                    self.logger.error(f"测试 {test_key} 失败: {e}")
                    all_results[test_key] = {'error': str(e)}
        
        # 保存综合结果
        self.results_manager.save_results("comprehensive_test", all_results)
        
        # 生成对比图
        self._plot_comparison_results(all_results)
        
        self.logger.info("综合测试完成")
        
        return all_results
    
    def _plot_comparison_results(self, results: Dict[str, Any]):
        """绘制对比结果"""
        test_names = []
        accuracies = []
        
        for test_name, result in results.items():
            if 'error' not in result:
                test_names.append(test_name)
                accuracies.append(result['test_accuracy'])
        
        if test_names:
            self.visualizer.plot_method_comparison(
                {name: {'accuracy': acc} for name, acc in zip(test_names, accuracies)},
                metric='accuracy',
                title='Comprehensive Test Results',
                save_name='comprehensive_test_comparison'
            )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="模型测试脚本")
    parser.add_argument('--config', type=str, default='configs/config.yaml', help='配置文件路径')
    parser.add_argument('--checkpoint', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--test-datasets', type=str, nargs='+', help='测试数据集列表')
    parser.add_argument('--test-methods', type=str, nargs='+', help='测试增强方法列表')
    parser.add_argument('--mode', type=str, choices=['single', 'comprehensive'], default='single', help='测试模式')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    # 设置随机种子
    set_seed(config['experiment']['seed'])
    
    # 获取设备
    device = get_device(config)
    
    # 创建测试器
    tester = ModelTester(config, device)
    
    logger.info(f"开始测试，模式: {args.mode}")
    
    if args.mode == 'single':
        # 单次测试
        model = tester.load_model(args.checkpoint)
        results = tester.test_model(model)
        
        # 可视化
        tester.visualize_results(results)
        
        # 保存结果
        tester.results_manager.save_results("single_test", results)
        
        logger.info(f"测试完成，准确率: {results['test_accuracy']:.4f}")
        
    elif args.mode == 'comprehensive':
        # 综合测试
        results = tester.run_comprehensive_test(
            checkpoint_path=args.checkpoint,
            test_datasets=args.test_datasets,
            test_augmentation_methods=args.test_methods
        )
        
        logger.info("综合测试完成")


if __name__ == "__main__":
    main()
