#!/usr/bin/env python3
"""
示例运行脚本
演示如何使用项目进行故障诊断实验
"""

import os
import sys
import subprocess
import argparse
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    os.makedirs("logs", exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(f'logs/run_example_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        ]
    )

def run_command(command, description):
    """运行命令并记录结果"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始: {description}")
    logger.info(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"✓ {description} 成功完成")
            if result.stdout:
                logger.info(f"输出: {result.stdout}")
        else:
            logger.error(f"✗ {description} 失败")
            if result.stderr:
                logger.error(f"错误: {result.stderr}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ {description} 异常: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="故障诊断项目示例运行脚本")
    parser.add_argument('--mode', type=str, 
                       choices=['quick_test', 'train_single', 'train_batch', 'test_model', 'full_demo'],
                       default='quick_test',
                       help='运行模式')
    parser.add_argument('--config', type=str, default='configs/config_kat_example.yaml', help='配置文件')
    parser.add_argument('--method', type=str, default='CDDPM', help='数据增强方法')
    
    args = parser.parse_args()
    
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("=" * 60)
    print("故障诊断数据增强项目 - 示例运行")
    print("=" * 60)
    
    logger.info(f"运行模式: {args.mode}")
    logger.info(f"配置文件: {args.config}")
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        logger.error(f"配置文件不存在: {args.config}")
        return False
    
    success = True
    
    if args.mode == 'quick_test':
        # 快速测试
        logger.info("运行快速测试...")
        success = run_command("python quick_test.py", "快速测试")
        
    elif args.mode == 'train_single':
        # 单独训练示例
        logger.info("运行单独训练示例...")
        
        # 1. 训练生成器
        cmd = f"python train.py --config {args.config} --mode generator --method {args.method}"
        success = run_command(cmd, f"训练生成器 ({args.method})")
        
        if success:
            # 2. 训练分类器（使用增强）
            cmd = f"python train.py --config {args.config} --mode classifier"
            success = run_command(cmd, "训练分类器（使用数据增强）")
        
        if success:
            # 3. 训练分类器（不使用增强，作为对比）
            cmd = f"python train.py --config {args.config} --mode classifier --no-augmentation"
            success = run_command(cmd, "训练分类器（不使用数据增强）")
    
    elif args.mode == 'train_batch':
        # 批量训练示例
        logger.info("运行批量训练示例...")
        
        # 运行方法对比实验
        cmd = f"python batch_train.py --config {args.config} --experiments method_comparison"
        success = run_command(cmd, "方法对比实验")
        
        if success:
            # 运行样本数量分析
            cmd = f"python batch_train.py --config {args.config} --experiments sample_count"
            success = run_command(cmd, "样本数量分析实验")
    
    elif args.mode == 'test_model':
        # 测试模型
        logger.info("运行模型测试...")
        
        # 检查是否有训练好的模型
        best_model_path = "checkpoints/best/best_model.pth"
        if not os.path.exists(best_model_path):
            logger.error(f"未找到训练好的模型: {best_model_path}")
            logger.info("请先运行训练模式")
            return False
        
        # 运行综合测试
        cmd = f"python test.py --config {args.config} --checkpoint {best_model_path} --mode comprehensive"
        success = run_command(cmd, "综合模型测试")
    
    elif args.mode == 'full_demo':
        # 完整演示
        logger.info("运行完整演示...")
        
        # 1. 快速测试
        logger.info("步骤 1/4: 快速测试")
        success = run_command("python quick_test.py", "快速测试")
        
        if success:
            # 2. 训练一个简单的生成器
            logger.info("步骤 2/4: 训练生成器")
            cmd = f"python train.py --config {args.config} --mode generator --method ADASYN"  # 使用快速的传统方法
            success = run_command(cmd, "训练生成器 (ADASYN)")
        
        if success:
            # 3. 训练分类器
            logger.info("步骤 3/4: 训练分类器")
            cmd = f"python train.py --config {args.config} --mode classifier"
            success = run_command(cmd, "训练分类器")
        
        if success:
            # 4. 测试模型
            logger.info("步骤 4/4: 测试模型")
            best_model_path = "checkpoints/best/best_model.pth"
            if os.path.exists(best_model_path):
                cmd = f"python test.py --config {args.config} --checkpoint {best_model_path} --mode single"
                success = run_command(cmd, "测试模型")
            else:
                logger.warning("未找到训练好的模型，跳过测试步骤")
    
    # 总结
    logger.info("\n" + "=" * 40)
    if success:
        logger.info("🎉 示例运行成功完成！")
        
        # 提供下一步建议
        logger.info("\n下一步建议:")
        if args.mode == 'quick_test':
            logger.info("- 运行 'python run_example.py --mode train_single' 进行单独训练")
            logger.info("- 运行 'python run_example.py --mode full_demo' 进行完整演示")
        elif args.mode in ['train_single', 'full_demo']:
            logger.info("- 查看 results/ 目录中的实验结果")
            logger.info("- 查看 results/plots/ 目录中的可视化图片")
            logger.info("- 运行 'python run_example.py --mode test_model' 进行模型测试")
        elif args.mode == 'train_batch':
            logger.info("- 查看 results/batch_experiments/ 目录中的批量实验结果")
            logger.info("- 运行 'python batch_train.py --experiments all' 进行所有实验")
        elif args.mode == 'test_model':
            logger.info("- 查看 results/ 目录中的测试结果")
            logger.info("- 查看 results/plots/ 目录中的测试可视化")
        
        logger.info("\n文件位置:")
        logger.info("- 训练日志: logs/")
        logger.info("- 模型权重: checkpoints/")
        logger.info("- 实验结果: results/")
        logger.info("- 生成样本: dataset/*/gen_samples/")
        
    else:
        logger.error("❌ 示例运行失败")
        logger.info("\n故障排除:")
        logger.info("1. 检查依赖是否正确安装: pip install -r requirements.txt")
        logger.info("2. 检查数据集是否正确放置")
        logger.info("3. 检查GPU环境配置")
        logger.info("4. 查看详细日志: logs/")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
