"""
训练器模块
包含各种模型的训练逻辑
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import os
import time
from typing import Dict, Any, Optional, Tuple, List
import logging
from tqdm import tqdm

from .evaluation import PerformanceMonitor
from .data_loader import FaultDiagnosisDataset

logger = logging.getLogger(__name__)


class EarlyStopping:
    """早停机制"""
    
    def __init__(
        self,
        patience: int = 15,
        min_delta: float = 1e-4,
        monitor: str = "val_accuracy",
        mode: str = "max"
    ):
        """
        初始化早停机制
        
        Args:
            patience: 耐心值
            min_delta: 最小改善量
            monitor: 监控指标
            mode: 模式 (max, min)
        """
        self.patience = patience
        self.min_delta = min_delta
        self.monitor = monitor
        self.mode = mode
        
        self.best_score = None
        self.counter = 0
        self.early_stop = False
        
        if mode == "max":
            self.is_better = lambda score, best: score > best + min_delta
        else:
            self.is_better = lambda score, best: score < best - min_delta
    
    def __call__(self, metrics: Dict[str, float]) -> bool:
        """
        检查是否应该早停
        
        Args:
            metrics: 指标字典
            
        Returns:
            是否早停
        """
        if self.monitor not in metrics:
            logger.warning(f"监控指标 {self.monitor} 不在指标中")
            return False
        
        score = metrics[self.monitor]
        
        if self.best_score is None:
            self.best_score = score
        elif self.is_better(score, self.best_score):
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
                logger.info(f"早停触发，最佳{self.monitor}: {self.best_score:.4f}")
        
        return self.early_stop


class ClassifierTrainer:
    """分类器训练器"""
    
    def __init__(
        self,
        model: nn.Module,
        config: Dict[str, Any],
        device: torch.device,
        save_dir: str = "checkpoints"
    ):
        """
        初始化分类器训练器
        
        Args:
            model: 模型
            config: 配置
            device: 设备
            save_dir: 保存目录
        """
        self.model = model.to(device)
        self.config = config
        self.device = device
        self.save_dir = save_dir
        
        # 创建保存目录
        os.makedirs(os.path.join(save_dir, "best"), exist_ok=True)
        os.makedirs(os.path.join(save_dir, "process"), exist_ok=True)
        
        # 训练配置
        training_config = config['training']
        
        # 优化器
        optimizer_config = training_config['optimizer']
        if optimizer_config['type'] == 'Adam':
            self.optimizer = optim.Adam(
                model.parameters(),
                lr=training_config['learning_rate'],
                weight_decay=training_config['weight_decay'],
                betas=optimizer_config.get('betas', [0.9, 0.999]),
                eps=optimizer_config.get('eps', 1e-8)
            )
        elif optimizer_config['type'] == 'SGD':
            self.optimizer = optim.SGD(
                model.parameters(),
                lr=training_config['learning_rate'],
                weight_decay=training_config['weight_decay'],
                momentum=optimizer_config.get('momentum', 0.9)
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_config['type']}")
        
        # 学习率调度器
        scheduler_config = training_config['scheduler']
        if scheduler_config['type'] == 'CosineAnnealingLR':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=scheduler_config.get('T_max', training_config['num_epochs']),
                eta_min=scheduler_config.get('eta_min', 1e-6)
            )
        elif scheduler_config['type'] == 'StepLR':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config.get('step_size', 30),
                gamma=scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_config['type'] == 'ReduceLROnPlateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode=scheduler_config.get('mode', 'max'),
                factor=scheduler_config.get('factor', 0.1),
                patience=scheduler_config.get('patience', 10)
            )
        else:
            self.scheduler = None
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss()
        
        # 早停
        early_stopping_config = training_config.get('early_stopping', {})
        if early_stopping_config.get('enabled', True):
            self.early_stopping = EarlyStopping(
                patience=early_stopping_config.get('patience', 15),
                min_delta=early_stopping_config.get('min_delta', 1e-4),
                monitor=early_stopping_config.get('monitor', 'val_accuracy'),
                mode=early_stopping_config.get('mode', 'max')
            )
        else:
            self.early_stopping = None
        
        # 性能监控
        self.performance_monitor = PerformanceMonitor()
        
        # 训练历史
        self.train_history = {
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': []
        }
        
        self.best_val_acc = 0.0
        self.best_epoch = 0
    
    def train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        correct = 0
        total = 0
        
        pbar = tqdm(train_loader, desc="Training")
        for batch_idx, (data, target) in enumerate(pbar):
            data, target = data.to(self.device), target.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            output = self.model(data)
            loss = self.criterion(output, target)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*correct/total:.2f}%'
            })
            
            # 记录性能
            if batch_idx % 10 == 0:
                self.performance_monitor.record_usage()
        
        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        self.model.eval()
        
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                output = self.model(data)
                loss = self.criterion(output, target)
                
                total_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def save_checkpoint(self, epoch: int, is_best: bool = False, is_process: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_val_acc': self.best_val_acc,
            'train_history': self.train_history,
            'config': self.config
        }
        
        if self.scheduler is not None:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        if is_best:
            # 保存最佳模型
            save_path = os.path.join(self.save_dir, "best", "best_model.pth")
            torch.save(checkpoint, save_path)
            logger.info(f"保存最佳模型: {save_path}")
        
        if is_process:
            # 保存过程模型
            save_path = os.path.join(self.save_dir, "process", f"model_epoch_{epoch}.pth")
            torch.save(checkpoint, save_path)
            logger.info(f"保存过程模型: {save_path}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if 'scheduler_state_dict' in checkpoint and self.scheduler is not None:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.best_val_acc = checkpoint.get('best_val_acc', 0.0)
        self.train_history = checkpoint.get('train_history', {
            'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []
        })
        
        logger.info(f"加载检查点: {checkpoint_path}")
        return checkpoint.get('epoch', 0)
    
    def train(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        num_epochs: Optional[int] = None,
        resume_from: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            num_epochs: 训练轮数
            resume_from: 恢复训练的检查点路径
            
        Returns:
            训练结果
        """
        if num_epochs is None:
            num_epochs = self.config['training']['num_epochs']
        
        start_epoch = 0
        if resume_from and os.path.exists(resume_from):
            start_epoch = self.load_checkpoint(resume_from)
            logger.info(f"从epoch {start_epoch}恢复训练")
        
        # 开始性能监控
        self.performance_monitor.start_monitoring()
        
        logger.info(f"开始训练，共{num_epochs}个epoch")
        
        for epoch in range(start_epoch, num_epochs):
            epoch_start_time = time.time()
            
            # 训练
            train_loss, train_acc = self.train_epoch(train_loader)
            
            # 验证
            val_loss, val_acc = self.validate_epoch(val_loader)
            
            # 更新学习率
            if self.scheduler is not None:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_acc)
                else:
                    self.scheduler.step()
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['train_acc'].append(train_acc)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['val_acc'].append(val_acc)
            
            # 检查是否是最佳模型
            is_best = val_acc > self.best_val_acc
            if is_best:
                self.best_val_acc = val_acc
                self.best_epoch = epoch
                self.save_checkpoint(epoch, is_best=True)
            
            # 保存过程检查点
            checkpoint_config = self.config['training'].get('checkpoint', {})
            if checkpoint_config.get('save_process', True):
                save_frequency = checkpoint_config.get('save_frequency', 5)
                if (epoch + 1) % save_frequency == 0:
                    self.save_checkpoint(epoch, is_process=True)
            
            epoch_time = time.time() - epoch_start_time
            
            logger.info(
                f"Epoch {epoch+1}/{num_epochs} - "
                f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}, "
                f"Time: {epoch_time:.2f}s"
            )
            
            # 早停检查
            if self.early_stopping is not None:
                metrics = {'val_accuracy': val_acc, 'val_loss': val_loss}
                if self.early_stopping(metrics):
                    logger.info(f"早停在epoch {epoch+1}")
                    break
        
        # 获取性能指标
        performance_metrics = self.performance_monitor.get_performance_metrics()
        
        # 训练结果
        results = {
            'best_val_acc': self.best_val_acc,
            'best_epoch': self.best_epoch,
            'train_history': self.train_history,
            'performance_metrics': performance_metrics,
            'total_epochs': epoch + 1
        }
        
        logger.info(f"训练完成，最佳验证准确率: {self.best_val_acc:.4f} (epoch {self.best_epoch+1})")
        
        return results
    
    def evaluate(self, test_loader: DataLoader) -> Tuple[float, np.ndarray, np.ndarray]:
        """
        评估模型
        
        Args:
            test_loader: 测试数据加载器
            
        Returns:
            准确率、真实标签、预测标签
        """
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                output = self.model(data)
                _, predicted = torch.max(output.data, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
        
        all_predictions = np.array(all_predictions)
        all_targets = np.array(all_targets)
        
        accuracy = (all_predictions == all_targets).mean()
        
        return accuracy, all_targets, all_predictions
    
    def extract_features(self, data_loader: DataLoader) -> Tuple[np.ndarray, np.ndarray]:
        """
        提取特征
        
        Args:
            data_loader: 数据加载器
            
        Returns:
            特征、标签
        """
        self.model.eval()
        
        all_features = []
        all_labels = []
        
        with torch.no_grad():
            for data, target in data_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                # 提取特征（假设模型有features方法）
                if hasattr(self.model, 'features'):
                    features = self.model.features(data)
                else:
                    # 如果没有features方法，使用最后一层前的特征
                    features = self.model(data)
                
                all_features.append(features.cpu().numpy())
                all_labels.append(target.cpu().numpy())
        
        all_features = np.vstack(all_features)
        all_labels = np.concatenate(all_labels)
        
        return all_features, all_labels


class GeneratorTrainer:
    """生成器训练器基类"""

    def __init__(
        self,
        model: nn.Module,
        config: Dict[str, Any],
        device: torch.device,
        save_dir: str = "checkpoints"
    ):
        """
        初始化生成器训练器

        Args:
            model: 生成模型
            config: 配置
            device: 设备
            save_dir: 保存目录
        """
        self.model = model.to(device)
        self.config = config
        self.device = device
        self.save_dir = save_dir

        # 创建保存目录
        os.makedirs(os.path.join(save_dir, "generators"), exist_ok=True)

        # 性能监控
        self.performance_monitor = PerformanceMonitor()

    def save_model(self, model_name: str, epoch: Optional[int] = None):
        """保存模型"""
        if epoch is not None:
            save_path = os.path.join(self.save_dir, "generators", f"{model_name}_epoch_{epoch}.pth")
        else:
            save_path = os.path.join(self.save_dir, "generators", f"{model_name}_final.pth")

        torch.save(self.model.state_dict(), save_path)
        logger.info(f"保存生成器模型: {save_path}")

    def load_model(self, model_path: str):
        """加载模型"""
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        logger.info(f"加载生成器模型: {model_path}")

    def generate_samples(
        self,
        num_samples_per_class: int,
        num_classes: int,
        signal_length: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成样本（需要在子类中实现）

        Args:
            num_samples_per_class: 每类生成的样本数
            num_classes: 类别数
            signal_length: 信号长度

        Returns:
            生成的数据和标签
        """
        raise NotImplementedError("子类必须实现generate_samples方法")


class CDDPMTrainer(GeneratorTrainer):
    """CDDPM训练器"""

    def __init__(
        self,
        model: nn.Module,
        config: Dict[str, Any],
        device: torch.device,
        save_dir: str = "checkpoints"
    ):
        super().__init__(model, config, device, save_dir)

        # CDDPM特定配置
        cddpm_config = config['training']['generator_training']['CDDPM']

        # 优化器
        self.optimizer = optim.Adam(
            model.parameters(),
            lr=cddpm_config['learning_rate'],
            betas=[0.9, 0.999]
        )

        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=cddpm_config['num_epochs']
        )

        self.num_epochs = cddpm_config['num_epochs']
        self.batch_size = cddpm_config['batch_size']

    def train(self, train_loader: DataLoader) -> Dict[str, Any]:
        """训练CDDPM模型"""
        self.performance_monitor.start_monitoring()

        logger.info(f"开始训练CDDPM，共{self.num_epochs}个epoch")

        train_losses = []

        for epoch in range(self.num_epochs):
            self.model.train()
            epoch_loss = 0.0
            num_batches = 0

            pbar = tqdm(train_loader, desc=f"CDDPM Epoch {epoch+1}/{self.num_epochs}")
            for batch_idx, (data, labels) in enumerate(pbar):
                data, labels = data.to(self.device), labels.to(self.device)

                # 确保数据格式正确
                if data.dim() == 2:
                    data = data.unsqueeze(1)  # [B, 1, L]

                # 前向传播
                self.optimizer.zero_grad()
                loss = self.model(data, labels)

                # 反向传播
                loss.backward()
                self.optimizer.step()

                epoch_loss += loss.item()
                num_batches += 1

                pbar.set_postfix({'Loss': f'{loss.item():.4f}'})

                if batch_idx % 10 == 0:
                    self.performance_monitor.record_usage()

            # 更新学习率
            self.scheduler.step()

            avg_loss = epoch_loss / num_batches
            train_losses.append(avg_loss)

            logger.info(f"CDDPM Epoch {epoch+1}/{self.num_epochs}, Loss: {avg_loss:.4f}")

            # 保存模型
            if (epoch + 1) % 50 == 0:
                self.save_model("cddpm", epoch + 1)

        # 保存最终模型
        self.save_model("cddpm")

        # 获取性能指标
        performance_metrics = self.performance_monitor.get_performance_metrics()

        results = {
            'train_losses': train_losses,
            'performance_metrics': performance_metrics
        }

        logger.info("CDDPM训练完成")
        return results

    def generate_samples(
        self,
        num_samples_per_class: int,
        num_classes: int,
        signal_length: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """生成CDDPM样本"""
        self.model.eval()

        generated_data = []
        generated_labels = []

        with torch.no_grad():
            for class_id in range(num_classes):
                # 创建类别标签
                labels = torch.full((num_samples_per_class,), class_id, device=self.device)

                # 生成样本
                samples = self.model.sample(num_samples_per_class, labels, self.device)

                generated_data.append(samples.cpu().numpy())
                generated_labels.extend([class_id] * num_samples_per_class)

        generated_data = np.vstack(generated_data)
        generated_labels = np.array(generated_labels)

        logger.info(f"CDDPM生成样本: {generated_data.shape}")
        return generated_data, generated_labels
