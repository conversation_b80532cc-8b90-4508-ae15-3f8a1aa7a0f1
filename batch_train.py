#!/usr/bin/env python3
"""
批量训练脚本
支持多种实验组合和批量测试
"""

import os
import sys
import yaml
import torch
import numpy as np
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, List, Tuple
import itertools
from copy import deepcopy

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from train import (
    setup_logging, load_config, set_seed, get_device,
    train_generator, train_traditional_augmentation, train_classifier
)
from common.evaluation import ResultsManager, Visualizer
from common.data_loader import DataLoaderManager


class BatchExperimentRunner:
    """批量实验运行器"""
    
    def __init__(self, base_config: Dict[str, Any]):
        """
        初始化批量实验运行器
        
        Args:
            base_config: 基础配置
        """
        self.base_config = base_config
        self.results_manager = ResultsManager()
        self.visualizer = Visualizer()
        self.logger = logging.getLogger(__name__)
        
        # 创建结果目录
        os.makedirs("results/batch_experiments", exist_ok=True)
    
    def run_sample_count_analysis(self) -> Dict[str, Any]:
        """运行样本数量分析实验"""
        self.logger.info("开始样本数量分析实验")
        
        analysis_config = self.base_config['analysis']['sample_count_analysis']
        if not analysis_config['enabled']:
            self.logger.info("样本数量分析实验已禁用")
            return {}
        
        sample_ratios = analysis_config['sample_ratios']
        results = {}
        
        for ratio in sample_ratios:
            self.logger.info(f"测试样本比例: {ratio}")
            
            # 修改配置
            config = deepcopy(self.base_config)
            
            # 计算样本数量
            dataset_name = config['dataset']['name']
            dataset_info = config['dataset']['datasets'][dataset_name]
            max_samples = int(dataset_info['train_samples'] * ratio)
            
            config['dataset']['data_loading']['max_train_samples'] = max_samples
            config['dataset']['augmentation']['enabled'] = False  # 不使用增强
            
            # 设置设备和种子
            device = get_device(config)
            set_seed(config['experiment']['seed'])
            
            try:
                # 训练分类器
                classifier_results = train_classifier(config, device, use_augmentation=False)
                
                results[f"ratio_{ratio}"] = {
                    'sample_count': max_samples,
                    'test_accuracy': classifier_results['test_accuracy'],
                    'metrics': classifier_results['metrics']
                }
                
                self.logger.info(f"样本比例 {ratio} 完成，准确率: {classifier_results['test_accuracy']:.4f}")
                
            except Exception as e:
                self.logger.error(f"样本比例 {ratio} 实验失败: {e}")
                results[f"ratio_{ratio}"] = {'error': str(e)}
        
        # 保存结果
        self.results_manager.save_results("sample_count_analysis", results)
        
        # 可视化
        if results:
            self._plot_sample_count_results(results)
        
        return results
    
    def run_generated_count_analysis(self) -> Dict[str, Any]:
        """运行生成样本数量分析实验"""
        self.logger.info("开始生成样本数量分析实验")
        
        analysis_config = self.base_config['analysis']['generated_count_analysis']
        if not analysis_config['enabled']:
            self.logger.info("生成样本数量分析实验已禁用")
            return {}
        
        generated_counts = analysis_config['generated_counts']
        current_method = self.base_config['dataset']['augmentation']['current_method']
        results = {}
        
        # 首先训练生成器（如果需要）
        if current_method in ["CDDPM", "CGAN", "WGAN", "WGAN_GP", "ACGAN", "DDPM"]:
            device = get_device(self.base_config)
            try:
                train_generator(current_method, self.base_config, device)
            except Exception as e:
                self.logger.error(f"训练生成器失败: {e}")
                return {}
        elif current_method in ["ADASYN", "SMOTEENN"]:
            try:
                train_traditional_augmentation(current_method, self.base_config)
            except Exception as e:
                self.logger.error(f"生成传统增强样本失败: {e}")
                return {}
        
        for count in generated_counts:
            self.logger.info(f"测试生成样本数量: {count}")
            
            # 修改配置
            config = deepcopy(self.base_config)
            config['dataset']['augmentation']['generated_samples_per_class'] = count
            config['dataset']['augmentation']['enabled'] = True
            
            # 设置设备和种子
            device = get_device(config)
            set_seed(config['experiment']['seed'])
            
            try:
                # 训练分类器
                classifier_results = train_classifier(config, device, use_augmentation=True)
                
                results[f"count_{count}"] = {
                    'generated_count': count,
                    'test_accuracy': classifier_results['test_accuracy'],
                    'metrics': classifier_results['metrics']
                }
                
                self.logger.info(f"生成数量 {count} 完成，准确率: {classifier_results['test_accuracy']:.4f}")
                
            except Exception as e:
                self.logger.error(f"生成数量 {count} 实验失败: {e}")
                results[f"count_{count}"] = {'error': str(e)}
        
        # 保存结果
        self.results_manager.save_results("generated_count_analysis", results)
        
        # 可视化
        if results:
            self._plot_generated_count_results(results)
        
        return results
    
    def run_noise_analysis(self) -> Dict[str, Any]:
        """运行噪声鲁棒性分析实验"""
        self.logger.info("开始噪声鲁棒性分析实验")
        
        analysis_config = self.base_config['analysis']['noise_analysis']
        if not analysis_config['enabled']:
            self.logger.info("噪声鲁棒性分析实验已禁用")
            return {}
        
        noise_levels = analysis_config['noise_levels']
        results = {}
        
        for noise_level in noise_levels:
            self.logger.info(f"测试噪声水平: {noise_level}")
            
            # 修改配置
            config = deepcopy(self.base_config)
            config['dataset']['data_loading']['add_noise'] = noise_level > 0
            config['dataset']['data_loading']['noise_level'] = noise_level
            config['dataset']['augmentation']['enabled'] = False  # 不使用增强
            
            # 设置设备和种子
            device = get_device(config)
            set_seed(config['experiment']['seed'])
            
            try:
                # 训练分类器
                classifier_results = train_classifier(config, device, use_augmentation=False)
                
                results[f"noise_{noise_level}"] = {
                    'noise_level': noise_level,
                    'test_accuracy': classifier_results['test_accuracy'],
                    'metrics': classifier_results['metrics']
                }
                
                self.logger.info(f"噪声水平 {noise_level} 完成，准确率: {classifier_results['test_accuracy']:.4f}")
                
            except Exception as e:
                self.logger.error(f"噪声水平 {noise_level} 实验失败: {e}")
                results[f"noise_{noise_level}"] = {'error': str(e)}
        
        # 保存结果
        self.results_manager.save_results("noise_analysis", results)
        
        # 可视化
        if results:
            self._plot_noise_results(results)
        
        return results
    
    def run_method_comparison(self) -> Dict[str, Any]:
        """运行方法对比实验"""
        self.logger.info("开始方法对比实验")
        
        methods = self.base_config['dataset']['augmentation']['methods']
        results = {}
        
        # 基线实验（无增强）
        self.logger.info("运行基线实验（无增强）")
        config = deepcopy(self.base_config)
        config['dataset']['augmentation']['enabled'] = False
        
        device = get_device(config)
        set_seed(config['experiment']['seed'])
        
        try:
            baseline_results = train_classifier(config, device, use_augmentation=False)
            results['baseline'] = {
                'method': 'baseline',
                'test_accuracy': baseline_results['test_accuracy'],
                'metrics': baseline_results['metrics']
            }
            self.logger.info(f"基线实验完成，准确率: {baseline_results['test_accuracy']:.4f}")
        except Exception as e:
            self.logger.error(f"基线实验失败: {e}")
            results['baseline'] = {'error': str(e)}
        
        # 各种增强方法
        for method in methods:
            self.logger.info(f"测试增强方法: {method}")
            
            # 修改配置
            config = deepcopy(self.base_config)
            config['dataset']['augmentation']['current_method'] = method
            config['dataset']['augmentation']['enabled'] = True
            
            # 设置设备和种子
            device = get_device(config)
            set_seed(config['experiment']['seed'])
            
            try:
                # 训练生成器
                if method in ["CDDPM", "CGAN", "WGAN", "WGAN_GP", "ACGAN", "DDPM"]:
                    train_generator(method, config, device)
                elif method in ["ADASYN", "SMOTEENN"]:
                    train_traditional_augmentation(method, config)
                
                # 训练分类器
                classifier_results = train_classifier(config, device, use_augmentation=True)
                
                results[method] = {
                    'method': method,
                    'test_accuracy': classifier_results['test_accuracy'],
                    'metrics': classifier_results['metrics']
                }
                
                self.logger.info(f"方法 {method} 完成，准确率: {classifier_results['test_accuracy']:.4f}")
                
            except Exception as e:
                self.logger.error(f"方法 {method} 实验失败: {e}")
                results[method] = {'error': str(e)}
        
        # 保存结果
        self.results_manager.save_results("method_comparison", results)
        
        # 可视化
        if results:
            self._plot_method_comparison_results(results)
        
        return results
    
    def run_combination_experiments(self) -> Dict[str, Any]:
        """运行组合实验"""
        self.logger.info("开始组合实验")
        
        batch_config = self.base_config.get('batch_experiments', {})
        combinations = batch_config.get('combinations', {})
        
        all_results = {}
        
        for exp_name, exp_config in combinations.items():
            self.logger.info(f"运行组合实验: {exp_name}")
            
            exp_results = {}
            
            if exp_name == "exp1":  # 样本数量 vs 生成方法
                sample_ratios = exp_config['sample_ratios']
                methods = exp_config['methods']
                
                for ratio, method in itertools.product(sample_ratios, methods):
                    exp_key = f"ratio_{ratio}_method_{method}"
                    self.logger.info(f"运行实验: {exp_key}")
                    
                    config = deepcopy(self.base_config)
                    
                    # 设置样本比例
                    dataset_name = config['dataset']['name']
                    dataset_info = config['dataset']['datasets'][dataset_name]
                    max_samples = int(dataset_info['train_samples'] * ratio)
                    config['dataset']['data_loading']['max_train_samples'] = max_samples
                    
                    # 设置增强方法
                    config['dataset']['augmentation']['current_method'] = method
                    config['dataset']['augmentation']['enabled'] = True
                    
                    device = get_device(config)
                    set_seed(config['experiment']['seed'])
                    
                    try:
                        # 训练生成器
                        if method in ["CDDPM", "CGAN", "WGAN", "WGAN_GP", "ACGAN", "DDPM"]:
                            train_generator(method, config, device)
                        elif method in ["ADASYN", "SMOTEENN"]:
                            train_traditional_augmentation(method, config)
                        
                        # 训练分类器
                        classifier_results = train_classifier(config, device, use_augmentation=True)
                        
                        exp_results[exp_key] = {
                            'sample_ratio': ratio,
                            'method': method,
                            'test_accuracy': classifier_results['test_accuracy'],
                            'metrics': classifier_results['metrics']
                        }
                        
                    except Exception as e:
                        self.logger.error(f"实验 {exp_key} 失败: {e}")
                        exp_results[exp_key] = {'error': str(e)}
            
            all_results[exp_name] = exp_results
            
            # 保存单个实验结果
            self.results_manager.save_results(f"combination_{exp_name}", exp_results)
        
        # 保存所有组合实验结果
        self.results_manager.save_results("all_combination_experiments", all_results)
        
        return all_results
    
    def _plot_sample_count_results(self, results: Dict[str, Any]):
        """绘制样本数量分析结果"""
        ratios = []
        accuracies = []
        
        for key, result in results.items():
            if 'error' not in result:
                ratio = float(key.split('_')[1])
                ratios.append(ratio)
                accuracies.append(result['test_accuracy'])
        
        if ratios:
            import matplotlib.pyplot as plt
            
            plt.figure(figsize=(10, 6))
            plt.plot(ratios, accuracies, 'bo-', linewidth=2, markersize=8)
            plt.xlabel('Sample Ratio')
            plt.ylabel('Test Accuracy')
            plt.title('Test Accuracy vs Sample Count')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            save_path = "results/batch_experiments/sample_count_analysis.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"样本数量分析图保存至: {save_path}")
    
    def _plot_generated_count_results(self, results: Dict[str, Any]):
        """绘制生成样本数量分析结果"""
        counts = []
        accuracies = []
        
        for key, result in results.items():
            if 'error' not in result:
                count = int(key.split('_')[1])
                counts.append(count)
                accuracies.append(result['test_accuracy'])
        
        if counts:
            import matplotlib.pyplot as plt
            
            plt.figure(figsize=(10, 6))
            plt.plot(counts, accuracies, 'ro-', linewidth=2, markersize=8)
            plt.xlabel('Generated Samples per Class')
            plt.ylabel('Test Accuracy')
            plt.title('Test Accuracy vs Generated Sample Count')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            save_path = "results/batch_experiments/generated_count_analysis.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"生成样本数量分析图保存至: {save_path}")
    
    def _plot_noise_results(self, results: Dict[str, Any]):
        """绘制噪声分析结果"""
        noise_levels = []
        accuracies = []
        
        for key, result in results.items():
            if 'error' not in result:
                noise_level = float(key.split('_')[1])
                noise_levels.append(noise_level)
                accuracies.append(result['test_accuracy'])
        
        if noise_levels:
            import matplotlib.pyplot as plt
            
            plt.figure(figsize=(10, 6))
            plt.plot(noise_levels, accuracies, 'go-', linewidth=2, markersize=8)
            plt.xlabel('Noise Level')
            plt.ylabel('Test Accuracy')
            plt.title('Test Accuracy vs Noise Level')
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            
            save_path = "results/batch_experiments/noise_analysis.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"噪声分析图保存至: {save_path}")
    
    def _plot_method_comparison_results(self, results: Dict[str, Any]):
        """绘制方法对比结果"""
        methods = []
        accuracies = []
        
        for key, result in results.items():
            if 'error' not in result:
                methods.append(key)
                accuracies.append(result['test_accuracy'])
        
        if methods:
            self.visualizer.plot_method_comparison(
                {method: {'accuracy': acc} for method, acc in zip(methods, accuracies)},
                metric='accuracy',
                title='Method Comparison - Test Accuracy',
                save_name='method_comparison'
            )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="批量训练脚本")
    parser.add_argument('--config', type=str, default='configs/config.yaml', help='配置文件路径')
    parser.add_argument('--experiments', type=str, nargs='+', 
                       choices=['sample_count', 'generated_count', 'noise', 'method_comparison', 'combination', 'all'],
                       default=['all'], help='要运行的实验类型')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    # 创建批量实验运行器
    runner = BatchExperimentRunner(config)
    
    logger.info(f"开始批量实验，实验类型: {args.experiments}")
    
    # 运行实验
    if 'all' in args.experiments:
        experiments = ['sample_count', 'generated_count', 'noise', 'method_comparison', 'combination']
    else:
        experiments = args.experiments
    
    all_results = {}
    
    for exp_type in experiments:
        try:
            if exp_type == 'sample_count':
                results = runner.run_sample_count_analysis()
                all_results['sample_count_analysis'] = results
            elif exp_type == 'generated_count':
                results = runner.run_generated_count_analysis()
                all_results['generated_count_analysis'] = results
            elif exp_type == 'noise':
                results = runner.run_noise_analysis()
                all_results['noise_analysis'] = results
            elif exp_type == 'method_comparison':
                results = runner.run_method_comparison()
                all_results['method_comparison'] = results
            elif exp_type == 'combination':
                results = runner.run_combination_experiments()
                all_results['combination_experiments'] = results
        except Exception as e:
            logger.error(f"实验 {exp_type} 失败: {e}")
    
    # 保存所有结果
    runner.results_manager.save_results("batch_all_results", all_results)
    
    logger.info("批量实验完成")


if __name__ == "__main__":
    main()
