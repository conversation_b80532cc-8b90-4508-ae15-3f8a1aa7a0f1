#!/usr/bin/env python3
"""
主训练脚本
支持单独训练和测试
"""

import os
import sys
import yaml
import torch
import torch.utils.data
from torch.utils.data import DataLoader
import numpy as np
import argparse
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from common.data_loader import DataLoaderManager
from common.trainer import ClassifierTrainer, CDDPMTrainer
from common.gan_trainer import GANTrainer
from common.evaluation import MetricsCalculator, Visualizer, ResultsManager
from models.mr_cnn import MRCNN
from models.cddpm import CDDPM
from models.gan_models import CGAN, WGAN, WGAN_GP, ACGAN
from models.traditional_augmentation import ADASYN, SMOTEENN, SimpleAugmentation


def setup_logging(config: Dict[str, Any]) -> None:
    """设置日志"""
    log_config = config['output']['logging']
    
    # 创建logs目录
    os.makedirs('logs', exist_ok=True)
    
    # 配置日志
    log_level = getattr(logging, log_config['level'])
    
    handlers = []
    
    # 控制台输出
    if log_config.get('console_output', True):
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        handlers.append(console_handler)
    
    # 文件输出
    if log_config.get('save_to_file', True):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"logs/train_{timestamp}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        handlers.append(file_handler)
    
    # 设置格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    for handler in handlers:
        handler.setFormatter(formatter)
    
    # 配置根日志器
    logging.basicConfig(
        level=log_level,
        handlers=handlers,
        force=True
    )


def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config


def set_seed(seed: int) -> None:
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_device(config: Dict[str, Any]) -> torch.device:
    """获取设备"""
    device_config = config['experiment']['device']
    
    if device_config == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_config)
    
    logging.info(f"使用设备: {device}")
    return device


def create_model(config: Dict[str, Any], device: torch.device) -> MRCNN:
    """创建分类器模型"""
    dataset_name = config['dataset']['name']
    dataset_info = config['dataset']['datasets'][dataset_name]
    
    model = MRCNN(
        config=config,
        num_classes=dataset_info['num_classes'],
        input_channels=1
    )
    
    return model.to(device)


def create_generator(method: str, config: Dict[str, Any], device: torch.device):
    """创建生成器模型"""
    dataset_name = config['dataset']['name']
    dataset_info = config['dataset']['datasets'][dataset_name]
    
    signal_length = config['dataset']['data_loading'].get('truncate_length') or dataset_info['signal_length']
    num_classes = dataset_info['num_classes']
    
    if method == "CDDPM":
        model = CDDPM(
            signal_length=signal_length,
            num_classes=num_classes,
            **config['models']['generators']['CDDPM']
        )
    elif method == "CGAN":
        model = CGAN(
            signal_length=signal_length,
            num_classes=num_classes,
            generator_config=config['models']['generators']['CGAN']['generator'],
            discriminator_config=config['models']['generators']['CGAN']['discriminator']
        )
    elif method == "WGAN":
        model = WGAN(
            signal_length=signal_length,
            num_classes=num_classes,
            generator_config=config['models']['generators']['WGAN']['generator'],
            discriminator_config=config['models']['generators']['WGAN']['discriminator'],
            clip_value=config['models']['generators']['WGAN']['clip_value']
        )
    elif method == "WGAN_GP":
        model = WGAN_GP(
            signal_length=signal_length,
            num_classes=num_classes,
            generator_config=config['models']['generators']['WGAN_GP']['generator'],
            discriminator_config=config['models']['generators']['WGAN_GP']['discriminator'],
            lambda_gp=config['models']['generators']['WGAN_GP']['lambda_gp']
        )
    elif method == "ACGAN":
        model = ACGAN(
            signal_length=signal_length,
            num_classes=num_classes,
            generator_config=config['models']['generators']['ACGAN']['generator'],
            discriminator_config=config['models']['generators']['ACGAN']['discriminator'],
            lambda_cls=config['models']['generators']['ACGAN']['lambda_cls']
        )
    else:
        raise ValueError(f"不支持的生成方法: {method}")
    
    return model.to(device)


def train_generator(method: str, config: Dict[str, Any], device: torch.device) -> None:
    """训练生成器"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始训练生成器: {method}")
    
    # 创建数据加载器
    data_manager = DataLoaderManager(config)
    dataset_name = config['dataset']['name']
    
    train_loader = data_manager.create_dataloader(
        dataset_name=dataset_name,
        data_type="train",
        batch_size=config['training']['generator_training'].get('CDDPM' if method == 'CDDPM' else 'GAN', {}).get('batch_size', 64),
        shuffle=True
    )
    
    # 创建生成器模型
    model = create_generator(method, config, device)
    
    # 创建训练器
    if method == "CDDPM":
        trainer = CDDPMTrainer(model, config, device)
    elif method in ["CGAN", "WGAN", "WGAN_GP", "ACGAN"]:
        trainer = GANTrainer(model, config, device, model_type=method)
    else:
        raise ValueError(f"不支持的生成方法: {method}")
    
    # 训练
    results = trainer.train(train_loader)
    
    # 生成样本并保存
    dataset_info = config['dataset']['datasets'][dataset_name]
    signal_length = config['dataset']['data_loading'].get('truncate_length') or dataset_info['signal_length']
    num_samples_per_class = config['dataset']['augmentation']['generated_samples_per_class']
    
    generated_data, generated_labels = trainer.generate_samples(
        num_samples_per_class=num_samples_per_class,
        num_classes=dataset_info['num_classes'],
        signal_length=signal_length
    )
    
    # 保存生成的样本
    save_dir = os.path.join("dataset", dataset_name, "gen_samples", method)
    os.makedirs(save_dir, exist_ok=True)
    
    np.save(os.path.join(save_dir, "generated_data.npy"), generated_data)
    np.save(os.path.join(save_dir, "generated_labels.npy"), generated_labels)
    
    logger.info(f"生成样本保存至: {save_dir}")
    logger.info(f"生成器 {method} 训练完成")


def train_traditional_augmentation(method: str, config: Dict[str, Any]) -> None:
    """训练传统增强方法"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始生成传统增强样本: {method}")
    
    # 创建数据加载器
    data_manager = DataLoaderManager(config)
    dataset_name = config['dataset']['name']
    
    # 加载训练数据
    train_data, train_labels = data_manager.load_dataset(
        dataset_name=dataset_name,
        data_type="train"
    )
    
    # 获取参数
    dataset_info = config['dataset']['datasets'][dataset_name]
    signal_length = config['dataset']['data_loading'].get('truncate_length') or dataset_info['signal_length']
    num_samples_per_class = config['dataset']['augmentation']['generated_samples_per_class']
    
    # 创建增强器
    if method == "ADASYN":
        augmenter = ADASYN(**config['models']['generators']['ADASYN'])
    elif method == "SMOTEENN":
        augmenter = SMOTEENN(**config['models']['generators']['SMOTEENN'])
    elif method == "SimpleAug":
        augmenter = SimpleAugmentation()
    else:
        raise ValueError(f"不支持的传统增强方法: {method}")
    
    # 生成样本
    if method in ["ADASYN", "SMOTEENN"]:
        generated_data, generated_labels = augmenter.generate_samples(
            train_data, train_labels, num_samples_per_class, signal_length
        )
    else:  # SimpleAug
        generated_data, generated_labels = augmenter.generate_samples(
            train_data, train_labels, num_samples_per_class, signal_length
        )
    
    # 保存生成的样本
    save_dir = os.path.join("dataset", dataset_name, "gen_samples", method)
    os.makedirs(save_dir, exist_ok=True)
    
    np.save(os.path.join(save_dir, "generated_data.npy"), generated_data)
    np.save(os.path.join(save_dir, "generated_labels.npy"), generated_labels)
    
    logger.info(f"传统增强样本保存至: {save_dir}")
    logger.info(f"传统增强方法 {method} 完成")


def train_classifier(config: Dict[str, Any], device: torch.device, use_augmentation: bool = True) -> Dict[str, Any]:
    """训练分类器"""
    logger = logging.getLogger(__name__)
    logger.info("开始训练分类器")
    
    # 创建数据加载器
    data_manager = DataLoaderManager(config)
    dataset_name = config['dataset']['name']
    
    # 加载原始数据
    train_loader = data_manager.create_dataloader(
        dataset_name=dataset_name,
        data_type="train",
        shuffle=True
    )
    
    val_loader = data_manager.create_dataloader(
        dataset_name=dataset_name,
        data_type="test",  # 使用测试集作为验证集
        shuffle=False
    )
    
    # 如果使用数据增强
    if use_augmentation and config['dataset']['augmentation']['enabled']:
        method = config['dataset']['augmentation']['current_method']
        logger.info(f"使用数据增强方法: {method}")
        
        try:
            # 加载生成的样本
            generated_data, generated_labels = data_manager.load_generated_samples(
                dataset_name=dataset_name,
                method_name=method
            )
            
            # 合并原始数据和生成数据
            original_data, original_labels = data_manager.load_dataset(
                dataset_name=dataset_name,
                data_type="train"
            )
            
            combined_data = np.vstack([original_data, generated_data])
            combined_labels = np.concatenate([original_labels, generated_labels])
            
            # 创建增强后的数据加载器
            from common.data_loader import FaultDiagnosisDataset
            augmented_dataset = FaultDiagnosisDataset(combined_data, combined_labels)
            train_loader = DataLoader(
                augmented_dataset,
                batch_size=config['training']['batch_size'],
                shuffle=True,
                num_workers=0,
                pin_memory=True if torch.cuda.is_available() else False
            )
            
            logger.info(f"使用增强数据训练，总样本数: {len(combined_data)}")
            
        except FileNotFoundError:
            logger.warning(f"未找到生成样本，使用原始数据训练")
    
    # 创建模型
    model = create_model(config, device)
    
    # 创建训练器
    trainer = ClassifierTrainer(model, config, device)
    
    # 训练
    results = trainer.train(train_loader, val_loader)
    
    # 评估
    test_acc, y_true, y_pred = trainer.evaluate(val_loader)
    
    # 计算详细指标
    dataset_info = config['dataset']['datasets'][dataset_name]
    metrics_calculator = MetricsCalculator(
        num_classes=dataset_info['num_classes']
    )
    
    metrics = metrics_calculator.calculate_basic_metrics(y_true, y_pred)
    confusion_matrix = metrics_calculator.calculate_confusion_matrix(y_true, y_pred)
    
    # 可视化
    visualizer = Visualizer()
    
    # 绘制混淆矩阵
    class_names = [f"Class_{i}" for i in range(dataset_info['num_classes'])]
    visualizer.plot_confusion_matrix(
        confusion_matrix,
        class_names,
        title=f"Confusion Matrix - {dataset_name}",
        save_name=f"confusion_matrix_{dataset_name}"
    )
    
    # 绘制训练曲线
    visualizer.plot_training_curves(
        results['train_history']['train_loss'],
        results['train_history']['val_loss'],
        results['train_history']['train_acc'],
        results['train_history']['val_acc'],
        title=f"Training Curves - {dataset_name}",
        save_name=f"training_curves_{dataset_name}"
    )
    
    # t-SNE可视化
    features, labels = trainer.extract_features(val_loader)
    visualizer.plot_tsne(
        features,
        labels,
        class_names,
        title=f"t-SNE Visualization - {dataset_name}",
        save_name=f"tsne_{dataset_name}"
    )
    
    # 整合结果
    final_results = {
        'training_results': results,
        'test_accuracy': test_acc,
        'metrics': metrics,
        'confusion_matrix': confusion_matrix.tolist(),
        'predictions': {
            'y_true': y_true.tolist(),
            'y_pred': y_pred.tolist()
        }
    }
    
    logger.info(f"分类器训练完成，测试准确率: {test_acc:.4f}")
    
    return final_results


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="故障诊断训练脚本")
    parser.add_argument('--config', type=str, default='configs/config.yaml', help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['generator', 'classifier', 'all'], default='all', help='训练模式')
    parser.add_argument('--method', type=str, help='生成方法（仅在generator模式下使用）')
    parser.add_argument('--no-augmentation', action='store_true', help='不使用数据增强训练分类器')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置日志
    setup_logging(config)
    logger = logging.getLogger(__name__)
    
    # 设置随机种子
    set_seed(config['experiment']['seed'])
    
    # 获取设备
    device = get_device(config)
    
    # 创建结果管理器
    results_manager = ResultsManager()
    
    logger.info(f"开始训练，模式: {args.mode}")
    
    if args.mode in ['generator', 'all']:
        # 训练生成器
        if args.method:
            methods = [args.method]
        else:
            methods = config['dataset']['augmentation']['methods']
        
        for method in methods:
            try:
                if method in ["CDDPM", "CGAN", "WGAN", "WGAN_GP", "ACGAN", "DDPM"]:
                    train_generator(method, config, device)
                elif method in ["ADASYN", "SMOTEENN"]:
                    train_traditional_augmentation(method, config)
                else:
                    logger.warning(f"未知的生成方法: {method}")
            except Exception as e:
                logger.error(f"训练生成器 {method} 失败: {e}")
    
    if args.mode in ['classifier', 'all']:
        # 训练分类器
        try:
            use_augmentation = not args.no_augmentation
            results = train_classifier(config, device, use_augmentation)
            
            # 保存结果
            experiment_name = f"classifier_{config['dataset']['name']}"
            if use_augmentation:
                experiment_name += f"_{config['dataset']['augmentation']['current_method']}"
            
            results_manager.save_results(experiment_name, results)
            
        except Exception as e:
            logger.error(f"训练分类器失败: {e}")
    
    logger.info("训练完成")


if __name__ == "__main__":
    main()
