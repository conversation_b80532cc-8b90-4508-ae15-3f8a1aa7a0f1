"""
评估模块
包含各种评估指标的计算和可视化功能
"""

import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report
)
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns
import time
import psutil
import GPUtil
from typing import Dict, List, Tuple, Any, Optional
import logging
import os
from collections import defaultdict

logger = logging.getLogger(__name__)


class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self, num_classes: int, class_names: Optional[List[str]] = None):
        """
        初始化指标计算器
        
        Args:
            num_classes: 类别数量
            class_names: 类别名称列表
        """
        self.num_classes = num_classes
        self.class_names = class_names or [f"Class_{i}" for i in range(num_classes)]
        
    def calculate_basic_metrics(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray
    ) -> Dict[str, float]:
        """
        计算基本分类指标
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            
        Returns:
            包含各种指标的字典
        """
        metrics = {}
        
        # 准确率
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        
        # 精确率、召回率、F1分数
        metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
        metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
        metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
        
        # 每个类别的指标
        precision_per_class = precision_score(y_true, y_pred, average=None, zero_division=0)
        recall_per_class = recall_score(y_true, y_pred, average=None, zero_division=0)
        f1_per_class = f1_score(y_true, y_pred, average=None, zero_division=0)
        
        for i, class_name in enumerate(self.class_names):
            metrics[f'precision_{class_name}'] = precision_per_class[i]
            metrics[f'recall_{class_name}'] = recall_per_class[i]
            metrics[f'f1_{class_name}'] = f1_per_class[i]
        
        return metrics
    
    def calculate_confusion_matrix(
        self,
        y_true: np.ndarray,
        y_pred: np.ndarray,
        normalize: bool = True
    ) -> np.ndarray:
        """
        计算混淆矩阵
        
        Args:
            y_true: 真实标签
            y_pred: 预测标签
            normalize: 是否归一化
            
        Returns:
            混淆矩阵
        """
        cm = confusion_matrix(y_true, y_pred)
        
        if normalize:
            cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        return cm
    
    def calculate_gan_metrics(
        self,
        model: nn.Module,
        real_train_loader: DataLoader,
        real_test_loader: DataLoader,
        generated_data: np.ndarray,
        generated_labels: np.ndarray,
        device: torch.device
    ) -> Dict[str, float]:
        """
        计算GAN-train和GAN-test指标
        
        Args:
            model: 分类模型
            real_train_loader: 真实训练数据加载器
            real_test_loader: 真实测试数据加载器
            generated_data: 生成的数据
            generated_labels: 生成的标签
            device: 设备
            
        Returns:
            GAN指标字典
        """
        metrics = {}
        
        # 创建生成数据的DataLoader
        from .data_loader import FaultDiagnosisDataset
        gen_dataset = FaultDiagnosisDataset(generated_data, generated_labels)
        gen_loader = DataLoader(gen_dataset, batch_size=32, shuffle=False)
        
        # GAN-train: 用生成数据训练，在真实测试集上评估
        logger.info("计算GAN-train指标...")
        gan_train_model = self._clone_model(model)
        self._train_model(gan_train_model, gen_loader, device)
        gan_train_acc = self._evaluate_model(gan_train_model, real_test_loader, device)
        metrics['gan_train'] = gan_train_acc
        
        # GAN-test: 用真实训练数据训练，在生成数据上评估
        logger.info("计算GAN-test指标...")
        gan_test_model = self._clone_model(model)
        self._train_model(gan_test_model, real_train_loader, device)
        gan_test_acc = self._evaluate_model(gan_test_model, gen_loader, device)
        metrics['gan_test'] = gan_test_acc
        
        return metrics
    
    def _clone_model(self, model: nn.Module) -> nn.Module:
        """克隆模型"""
        import copy
        return copy.deepcopy(model)
    
    def _train_model(
        self,
        model: nn.Module,
        train_loader: DataLoader,
        device: torch.device,
        epochs: int = 50
    ):
        """训练模型"""
        model.to(device)
        model.train()
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        for epoch in range(epochs):
            for batch_idx, (data, target) in enumerate(train_loader):
                data, target = data.to(device), target.to(device)
                
                optimizer.zero_grad()
                output = model(data)
                loss = criterion(output, target)
                loss.backward()
                optimizer.step()
    
    def _evaluate_model(
        self,
        model: nn.Module,
        test_loader: DataLoader,
        device: torch.device
    ) -> float:
        """评估模型"""
        model.to(device)
        model.eval()
        
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(device), target.to(device)
                output = model(data)
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
        
        return correct / total


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = None
        self.gpu_usage = []
        self.memory_usage = []
        
    def start_monitoring(self):
        """开始监控"""
        self.start_time = time.time()
        self.gpu_usage = []
        self.memory_usage = []
        
    def record_usage(self):
        """记录使用情况"""
        # GPU使用率
        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu_usage = gpus[0].load * 100
                gpu_memory = gpus[0].memoryUtil * 100
                self.gpu_usage.append({'usage': gpu_usage, 'memory': gpu_memory})
        except:
            pass
        
        # CPU和内存使用率
        cpu_usage = psutil.cpu_percent()
        memory_usage = psutil.virtual_memory().percent
        self.memory_usage.append({'cpu': cpu_usage, 'memory': memory_usage})
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if self.start_time is None:
            return {}
        
        elapsed_time = time.time() - self.start_time
        
        metrics = {
            'elapsed_time': elapsed_time,
            'elapsed_time_formatted': f"{elapsed_time:.2f}s"
        }
        
        if self.gpu_usage:
            avg_gpu_usage = np.mean([x['usage'] for x in self.gpu_usage])
            avg_gpu_memory = np.mean([x['memory'] for x in self.gpu_usage])
            max_gpu_memory = np.max([x['memory'] for x in self.gpu_usage])
            
            metrics.update({
                'avg_gpu_usage': avg_gpu_usage,
                'avg_gpu_memory': avg_gpu_memory,
                'max_gpu_memory': max_gpu_memory
            })
        
        if self.memory_usage:
            avg_cpu_usage = np.mean([x['cpu'] for x in self.memory_usage])
            avg_memory_usage = np.mean([x['memory'] for x in self.memory_usage])
            
            metrics.update({
                'avg_cpu_usage': avg_cpu_usage,
                'avg_memory_usage': avg_memory_usage
            })
        
        return metrics


class Visualizer:
    """可视化器"""
    
    def __init__(self, save_dir: str = "results/plots"):
        """
        初始化可视化器
        
        Args:
            save_dir: 保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # 设置matplotlib参数
        plt.rcParams['font.size'] = 12
        plt.rcParams['figure.figsize'] = (10, 8)
        
    def plot_confusion_matrix(
        self,
        cm: np.ndarray,
        class_names: List[str],
        title: str = "Confusion Matrix",
        save_name: Optional[str] = None,
        normalize: bool = True
    ):
        """
        绘制混淆矩阵
        
        Args:
            cm: 混淆矩阵
            class_names: 类别名称
            title: 图标题
            save_name: 保存文件名
            normalize: 是否归一化显示
        """
        plt.figure(figsize=(10, 8))
        
        if normalize:
            fmt = '.2f'
            cmap = 'Blues'
        else:
            fmt = 'd'
            cmap = 'Blues'
        
        sns.heatmap(
            cm,
            annot=True,
            fmt=fmt,
            cmap=cmap,
            xticklabels=class_names,
            yticklabels=class_names,
            cbar_kws={'label': 'Normalized Count' if normalize else 'Count'}
        )
        
        plt.title(title)
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        
        if save_name:
            save_path = os.path.join(self.save_dir, f"{save_name}.png")
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"混淆矩阵保存至: {save_path}")
        
        plt.show()
        plt.close()
    
    def plot_tsne(
        self,
        features: np.ndarray,
        labels: np.ndarray,
        class_names: List[str],
        title: str = "t-SNE Visualization",
        save_name: Optional[str] = None,
        perplexity: int = 30,
        n_iter: int = 1000
    ):
        """
        绘制t-SNE可视化
        
        Args:
            features: 特征向量
            labels: 标签
            class_names: 类别名称
            title: 图标题
            save_name: 保存文件名
            perplexity: t-SNE参数
            n_iter: 迭代次数
        """
        logger.info("计算t-SNE降维...")
        
        # 执行t-SNE降维
        tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42)
        features_2d = tsne.fit_transform(features)
        
        # 绘制散点图
        plt.figure(figsize=(12, 10))
        
        colors = plt.cm.tab10(np.linspace(0, 1, len(class_names)))
        
        for i, class_name in enumerate(class_names):
            mask = labels == i
            if np.any(mask):
                plt.scatter(
                    features_2d[mask, 0],
                    features_2d[mask, 1],
                    c=[colors[i]],
                    label=class_name,
                    alpha=0.7,
                    s=50
                )
        
        plt.title(title)
        plt.xlabel('t-SNE Component 1')
        plt.ylabel('t-SNE Component 2')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_name:
            save_path = os.path.join(self.save_dir, f"{save_name}.png")
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"t-SNE图保存至: {save_path}")
        
        plt.show()
        plt.close()
    
    def plot_training_curves(
        self,
        train_losses: List[float],
        val_losses: List[float],
        train_accs: List[float],
        val_accs: List[float],
        title: str = "Training Curves",
        save_name: Optional[str] = None
    ):
        """
        绘制训练曲线
        
        Args:
            train_losses: 训练损失
            val_losses: 验证损失
            train_accs: 训练准确率
            val_accs: 验证准确率
            title: 图标题
            save_name: 保存文件名
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 损失曲线
        epochs = range(1, len(train_losses) + 1)
        ax1.plot(epochs, train_losses, 'b-', label='Training Loss')
        ax1.plot(epochs, val_losses, 'r-', label='Validation Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 准确率曲线
        ax2.plot(epochs, train_accs, 'b-', label='Training Accuracy')
        ax2.plot(epochs, val_accs, 'r-', label='Validation Accuracy')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.suptitle(title)
        plt.tight_layout()
        
        if save_name:
            save_path = os.path.join(self.save_dir, f"{save_name}.png")
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"训练曲线保存至: {save_path}")
        
        plt.show()
        plt.close()
    
    def plot_method_comparison(
        self,
        results: Dict[str, Dict[str, float]],
        metric: str = "accuracy",
        title: Optional[str] = None,
        save_name: Optional[str] = None
    ):
        """
        绘制方法对比图
        
        Args:
            results: 结果字典 {method_name: {metric: value}}
            metric: 要比较的指标
            title: 图标题
            save_name: 保存文件名
        """
        methods = list(results.keys())
        values = [results[method].get(metric, 0) for method in methods]
        
        plt.figure(figsize=(12, 8))
        
        bars = plt.bar(methods, values, color='skyblue', alpha=0.7)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{value:.3f}', ha='center', va='bottom')
        
        plt.title(title or f'{metric.capitalize()} Comparison')
        plt.xlabel('Methods')
        plt.ylabel(metric.capitalize())
        plt.xticks(rotation=45, ha='right')
        plt.grid(True, alpha=0.3, axis='y')
        plt.tight_layout()
        
        if save_name:
            save_path = os.path.join(self.save_dir, f"{save_name}.png")
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"方法对比图保存至: {save_path}")
        
        plt.show()
        plt.close()


class ResultsManager:
    """结果管理器"""
    
    def __init__(self, save_dir: str = "results"):
        """
        初始化结果管理器
        
        Args:
            save_dir: 保存目录
        """
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        self.results = defaultdict(dict)
    
    def save_results(
        self,
        experiment_name: str,
        results: Dict[str, Any],
        save_format: str = "json"
    ):
        """
        保存实验结果
        
        Args:
            experiment_name: 实验名称
            results: 结果字典
            save_format: 保存格式 (json, pickle)
        """
        self.results[experiment_name] = results
        
        if save_format == "json":
            import json
            save_path = os.path.join(self.save_dir, f"{experiment_name}.json")
            
            # 转换numpy数组为列表
            json_results = self._convert_for_json(results)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        elif save_format == "pickle":
            import pickle
            save_path = os.path.join(self.save_dir, f"{experiment_name}.pkl")
            
            with open(save_path, 'wb') as f:
                pickle.dump(results, f)
        
        logger.info(f"结果保存至: {save_path}")
    
    def _convert_for_json(self, obj):
        """转换对象为JSON可序列化格式"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_for_json(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(item) for item in obj]
        else:
            return obj
    
    def load_results(self, experiment_name: str, save_format: str = "json") -> Dict[str, Any]:
        """
        加载实验结果
        
        Args:
            experiment_name: 实验名称
            save_format: 保存格式
            
        Returns:
            结果字典
        """
        if save_format == "json":
            import json
            save_path = os.path.join(self.save_dir, f"{experiment_name}.json")
            
            with open(save_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
        
        elif save_format == "pickle":
            import pickle
            save_path = os.path.join(self.save_dir, f"{experiment_name}.pkl")
            
            with open(save_path, 'rb') as f:
                results = pickle.load(f)
        
        return results
    
    def get_summary(self) -> Dict[str, Any]:
        """获取所有实验的摘要"""
        summary = {}
        
        for exp_name, results in self.results.items():
            if 'metrics' in results:
                summary[exp_name] = {
                    'accuracy': results['metrics'].get('accuracy', 0),
                    'f1_score': results['metrics'].get('f1_score', 0),
                    'gan_train': results['metrics'].get('gan_train', 0),
                    'gan_test': results['metrics'].get('gan_test', 0)
                }
        
        return summary
