"""
传统数据增强方法
包括ADASYN, SMOTEENN等
"""

import numpy as np
import torch
from sklearn.neighbors import NearestNeighbors
from imblearn.over_sampling import ADASYN as ADASYN_sklearn
from imblearn.combine import SMOTEENN as SMOTEENN_sklearn
from typing import Tuple, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class ADASYN:
    """
    Adaptive Synthetic Sampling (ADASYN)
    自适应合成采样方法
    """
    
    def __init__(
        self,
        n_neighbors: int = 5,
        random_state: Optional[int] = None,
        sampling_strategy: str = 'auto'
    ):
        """
        初始化ADASYN
        
        Args:
            n_neighbors: 近邻数量
            random_state: 随机种子
            sampling_strategy: 采样策略
        """
        self.n_neighbors = n_neighbors
        self.random_state = random_state
        self.sampling_strategy = sampling_strategy
        
        # 使用sklearn的ADASYN实现
        self.adasyn = ADASYN_sklearn(
            n_neighbors=n_neighbors,
            random_state=random_state,
            sampling_strategy=sampling_strategy
        )
    
    def fit_resample(
        self,
        X: np.ndarray,
        y: np.ndarray,
        target_samples_per_class: Optional[int] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        拟合并重采样数据
        
        Args:
            X: 输入数据 [n_samples, n_features]
            y: 标签 [n_samples]
            target_samples_per_class: 目标每类样本数
            
        Returns:
            重采样后的数据和标签
        """
        logger.info(f"ADASYN重采样前: {X.shape}, 标签分布: {np.bincount(y)}")
        
        # 如果指定了目标样本数，调整采样策略
        if target_samples_per_class is not None:
            unique_labels = np.unique(y)
            sampling_strategy = {}
            for label in unique_labels:
                current_count = np.sum(y == label)
                if target_samples_per_class > current_count:
                    sampling_strategy[label] = target_samples_per_class
            
            if sampling_strategy:
                self.adasyn.sampling_strategy = sampling_strategy
        
        try:
            X_resampled, y_resampled = self.adasyn.fit_resample(X, y)
            logger.info(f"ADASYN重采样后: {X_resampled.shape}, 标签分布: {np.bincount(y_resampled)}")
            return X_resampled, y_resampled
        except Exception as e:
            logger.warning(f"ADASYN重采样失败: {e}, 返回原始数据")
            return X, y
    
    def generate_samples(
        self,
        X: np.ndarray,
        y: np.ndarray,
        num_samples_per_class: int,
        signal_length: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成指定数量的样本
        
        Args:
            X: 原始数据
            y: 原始标签
            num_samples_per_class: 每类生成的样本数
            signal_length: 信号长度
            
        Returns:
            生成的数据和标签
        """
        # 重塑数据为2D格式
        if X.ndim == 3:
            X_2d = X.reshape(X.shape[0], -1)
        else:
            X_2d = X
        
        # 计算目标样本数
        unique_labels = np.unique(y)
        target_samples = {}
        for label in unique_labels:
            current_count = np.sum(y == label)
            target_samples[label] = current_count + num_samples_per_class
        
        # 设置采样策略
        self.adasyn.sampling_strategy = target_samples
        
        try:
            X_resampled, y_resampled = self.adasyn.fit_resample(X_2d, y)
            
            # 提取生成的样本
            generated_indices = []
            for label in unique_labels:
                original_count = np.sum(y == label)
                label_indices = np.where(y_resampled == label)[0]
                # 取后面生成的样本
                generated_indices.extend(label_indices[original_count:])
            
            generated_X = X_resampled[generated_indices]
            generated_y = y_resampled[generated_indices]
            
            # 重塑回原始格式
            if X.ndim == 3:
                generated_X = generated_X.reshape(-1, 1, signal_length)
            
            logger.info(f"ADASYN生成样本: {generated_X.shape}")
            return generated_X, generated_y
            
        except Exception as e:
            logger.error(f"ADASYN生成样本失败: {e}")
            # 返回空数组
            if X.ndim == 3:
                return np.array([]).reshape(0, 1, signal_length), np.array([])
            else:
                return np.array([]).reshape(0, X.shape[1]), np.array([])


class SMOTEENN:
    """
    SMOTE + Edited Nearest Neighbours
    SMOTE过采样 + 编辑最近邻欠采样的组合方法
    """
    
    def __init__(
        self,
        smote_k_neighbors: int = 5,
        enn_n_neighbors: int = 3,
        random_state: Optional[int] = None,
        sampling_strategy: str = 'auto'
    ):
        """
        初始化SMOTEENN
        
        Args:
            smote_k_neighbors: SMOTE的近邻数量
            enn_n_neighbors: ENN的近邻数量
            random_state: 随机种子
            sampling_strategy: 采样策略
        """
        self.smote_k_neighbors = smote_k_neighbors
        self.enn_n_neighbors = enn_n_neighbors
        self.random_state = random_state
        self.sampling_strategy = sampling_strategy
        
        # 使用sklearn的SMOTEENN实现
        self.smoteenn = SMOTEENN_sklearn(
            smote=None,  # 使用默认SMOTE
            enn=None,    # 使用默认ENN
            random_state=random_state,
            sampling_strategy=sampling_strategy
        )
    
    def fit_resample(
        self,
        X: np.ndarray,
        y: np.ndarray,
        target_samples_per_class: Optional[int] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        拟合并重采样数据
        
        Args:
            X: 输入数据 [n_samples, n_features]
            y: 标签 [n_samples]
            target_samples_per_class: 目标每类样本数
            
        Returns:
            重采样后的数据和标签
        """
        logger.info(f"SMOTEENN重采样前: {X.shape}, 标签分布: {np.bincount(y)}")
        
        # 如果指定了目标样本数，调整采样策略
        if target_samples_per_class is not None:
            unique_labels = np.unique(y)
            sampling_strategy = {}
            for label in unique_labels:
                current_count = np.sum(y == label)
                if target_samples_per_class > current_count:
                    sampling_strategy[label] = target_samples_per_class
            
            if sampling_strategy:
                self.smoteenn.sampling_strategy = sampling_strategy
        
        try:
            X_resampled, y_resampled = self.smoteenn.fit_resample(X, y)
            logger.info(f"SMOTEENN重采样后: {X_resampled.shape}, 标签分布: {np.bincount(y_resampled)}")
            return X_resampled, y_resampled
        except Exception as e:
            logger.warning(f"SMOTEENN重采样失败: {e}, 返回原始数据")
            return X, y
    
    def generate_samples(
        self,
        X: np.ndarray,
        y: np.ndarray,
        num_samples_per_class: int,
        signal_length: int
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成指定数量的样本
        
        Args:
            X: 原始数据
            y: 原始标签
            num_samples_per_class: 每类生成的样本数
            signal_length: 信号长度
            
        Returns:
            生成的数据和标签
        """
        # 重塑数据为2D格式
        if X.ndim == 3:
            X_2d = X.reshape(X.shape[0], -1)
        else:
            X_2d = X
        
        # 计算目标样本数
        unique_labels = np.unique(y)
        target_samples = {}
        for label in unique_labels:
            current_count = np.sum(y == label)
            target_samples[label] = current_count + num_samples_per_class
        
        # 设置采样策略
        self.smoteenn.sampling_strategy = target_samples
        
        try:
            X_resampled, y_resampled = self.smoteenn.fit_resample(X_2d, y)
            
            # 由于SMOTEENN可能会删除一些原始样本，我们需要找到新增的样本
            # 这里简化处理，返回所有重采样后的数据
            generated_X = X_resampled
            generated_y = y_resampled
            
            # 重塑回原始格式
            if X.ndim == 3:
                generated_X = generated_X.reshape(-1, 1, signal_length)
            
            logger.info(f"SMOTEENN生成样本: {generated_X.shape}")
            return generated_X, generated_y
            
        except Exception as e:
            logger.error(f"SMOTEENN生成样本失败: {e}")
            # 返回空数组
            if X.ndim == 3:
                return np.array([]).reshape(0, 1, signal_length), np.array([])
            else:
                return np.array([]).reshape(0, X.shape[1]), np.array([])


class SimpleAugmentation:
    """
    简单的数据增强方法
    包括噪声添加、时间偏移、幅度缩放等
    """
    
    def __init__(self, random_state: Optional[int] = None):
        """
        初始化简单增强器
        
        Args:
            random_state: 随机种子
        """
        self.random_state = random_state
        if random_state is not None:
            np.random.seed(random_state)
    
    def add_noise(
        self,
        X: np.ndarray,
        noise_level: float = 0.1,
        noise_type: str = 'gaussian'
    ) -> np.ndarray:
        """
        添加噪声
        
        Args:
            X: 输入数据
            noise_level: 噪声水平
            noise_type: 噪声类型
            
        Returns:
            加噪后的数据
        """
        if noise_type == 'gaussian':
            noise = np.random.normal(0, noise_level, X.shape)
        elif noise_type == 'uniform':
            noise = np.random.uniform(-noise_level, noise_level, X.shape)
        else:
            raise ValueError(f"不支持的噪声类型: {noise_type}")
        
        return X + noise
    
    def time_shift(
        self,
        X: np.ndarray,
        max_shift: int = 10
    ) -> np.ndarray:
        """
        时间偏移
        
        Args:
            X: 输入数据 [n_samples, 1, signal_length]
            max_shift: 最大偏移量
            
        Returns:
            偏移后的数据
        """
        augmented_X = []
        
        for sample in X:
            shift = np.random.randint(-max_shift, max_shift + 1)
            if shift > 0:
                # 右移
                shifted = np.concatenate([np.zeros((1, shift)), sample[:, :-shift]], axis=1)
            elif shift < 0:
                # 左移
                shifted = np.concatenate([sample[:, -shift:], np.zeros((1, -shift))], axis=1)
            else:
                shifted = sample
            
            augmented_X.append(shifted)
        
        return np.array(augmented_X)
    
    def amplitude_scaling(
        self,
        X: np.ndarray,
        scale_range: Tuple[float, float] = (0.8, 1.2)
    ) -> np.ndarray:
        """
        幅度缩放
        
        Args:
            X: 输入数据
            scale_range: 缩放范围
            
        Returns:
            缩放后的数据
        """
        scales = np.random.uniform(scale_range[0], scale_range[1], (X.shape[0], 1, 1))
        return X * scales
    
    def generate_samples(
        self,
        X: np.ndarray,
        y: np.ndarray,
        num_samples_per_class: int,
        signal_length: int,
        augmentation_methods: list = ['noise', 'time_shift', 'amplitude_scaling']
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成增强样本
        
        Args:
            X: 原始数据
            y: 原始标签
            num_samples_per_class: 每类生成的样本数
            signal_length: 信号长度
            augmentation_methods: 增强方法列表
            
        Returns:
            生成的数据和标签
        """
        unique_labels = np.unique(y)
        generated_X = []
        generated_y = []
        
        for label in unique_labels:
            # 获取该类别的样本
            label_indices = np.where(y == label)[0]
            label_samples = X[label_indices]
            
            # 生成指定数量的样本
            for _ in range(num_samples_per_class):
                # 随机选择一个原始样本
                idx = np.random.choice(len(label_samples))
                sample = label_samples[idx:idx+1].copy()
                
                # 随机选择增强方法
                method = np.random.choice(augmentation_methods)
                
                if method == 'noise':
                    sample = self.add_noise(sample, noise_level=0.1)
                elif method == 'time_shift':
                    sample = self.time_shift(sample, max_shift=10)
                elif method == 'amplitude_scaling':
                    sample = self.amplitude_scaling(sample, scale_range=(0.8, 1.2))
                
                generated_X.append(sample[0])
                generated_y.append(label)
        
        generated_X = np.array(generated_X)
        generated_y = np.array(generated_y)
        
        # 确保格式正确
        if generated_X.ndim == 2:
            generated_X = generated_X[:, None, :]  # [n_samples, 1, signal_length]
        
        logger.info(f"简单增强生成样本: {generated_X.shape}")
        return generated_X, generated_y
